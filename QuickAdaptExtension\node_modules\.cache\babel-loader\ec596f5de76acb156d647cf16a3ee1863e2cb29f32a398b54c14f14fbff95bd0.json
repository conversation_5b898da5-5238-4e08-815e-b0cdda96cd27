{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\Imagesection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip, Snackbar, Alert } from \"@mui/material\";\nimport RemoveIcon from \"@mui/icons-material/Remove\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport { useTranslation } from 'react-i18next';\nimport { uploadfile, hyperlink, files, uploadicon, replaceimageicon, copyicon, deleteicon, sectionheight, Settings, CrossIcon } from \"../../../assets/icons/icons\";\nimport useDrawerStore, { IMG_CONTAINER_DEFAULT_HEIGHT, IMG_CONTAINER_MAX_HEIGHT, IMG_CONTAINER_MIN_HEIGHT, IMG_OBJECT_FIT, IMG_STEP_VALUE } from \"../../../store/drawerStore\";\nimport { ChromePicker } from \"react-color\";\nimport \"./PopupSections.css\";\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageSection = ({\n  setImageSrc,\n  setImageName,\n  onDelete,\n  onClone,\n  isCloneDisabled\n}) => {\n  _s();\n  var _imagesContainer$find;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    uploadImage,\n    imagesContainer,\n    imageAnchorEl,\n    setImageAnchorEl,\n    replaceImage,\n    cloneImageContainer,\n    deleteImageContainer,\n    updateImageContainer,\n    toggleFit,\n    setImageSrc: storeImageSrc\n  } = useDrawerStore(state => state);\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState('');\n  const [snackbarSeverity, setSnackbarSeverity] = useState('info');\n  const [snackbarKey, setSnackbarKey] = useState(0);\n  const openSnackbar = () => {\n    setSnackbarKey(prev => prev + 1);\n    setSnackbarOpen(true);\n  };\n  const closeSnackbar = () => {\n    setSnackbarOpen(false);\n  };\n  const [showHyperlinkInput, setShowHyperlinkInput] = useState({\n    currentContainerId: \"\",\n    isOpen: false\n  });\n  const [imageLink, setImageLink] = useState(\"\");\n  const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState(null);\n  const [currentImageSectionInfo, setCurrentImageSectionInfo] = useState({\n    currentContainerId: \"\",\n    isImage: false,\n    height: IMG_CONTAINER_DEFAULT_HEIGHT\n  });\n  const [selectedAction, setSelectedAction] = useState(\"none\");\n  const [isModelOpen, setModelOpen] = useState(false);\n  const [formOfUpload, setFormOfUpload] = useState(\"\");\n  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);\n  const [selectedColor, setSelectedColor] = useState(\"#313030\");\n  const [isReplaceImage, setReplaceImage] = useState(false);\n  const guidePopupRef = useRef(null);\n  const [popoverPositions, setPopoverPositions] = useState({\n    imagePopover: {},\n    settingsPopover: {}\n  });\n  const openSettingsPopover = Boolean(settingsAnchorEl);\n  const handleActionChange = event => {\n    setSelectedAction(event.target.value);\n  };\n  const handleSettingsClick = event => {\n    setSettingsAnchorEl(event.currentTarget);\n  };\n  const handleCloseSettingsPopover = () => {\n    setSettingsAnchorEl(null);\n  };\n  const imageContainerStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    padding: 0,\n    margin: 0,\n    overflow: \"hidden\"\n  };\n  const imageStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    margin: 0,\n    padding: 0,\n    borderRadius: \"0\"\n  };\n  const iconRowStyle = {\n    display: \"flex\",\n    justifyContent: \"center\",\n    gap: \"16px\",\n    marginTop: \"10px\"\n  };\n  const iconTextStyle = {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    width: \"100%\"\n  };\n  const handleImageUpload = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      var _event$target$files2;\n      const parts = file.name.split('.');\n      const extension = parts.pop();\n      if (parts.length > 1 || !extension) {\n        setSnackbarMessage(\"Uploaded file name should not contain any special character\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      if (file.name.length > 128) {\n        setSnackbarMessage(\"File name should not exceed 128 characters\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      setImageName((_event$target$files2 = event.target.files) === null || _event$target$files2 === void 0 ? void 0 : _event$target$files2[0].name);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        const base64Image = reader.result;\n        storeImageSrc(base64Image);\n        setImageSrc(base64Image);\n        uploadImage(imageAnchorEl.containerId, {\n          altText: file.name,\n          id: crypto.randomUUID(),\n          url: base64Image,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n    setModelOpen(false);\n  };\n  const handleImageUploadFormApp = file => {\n    if (file) {\n      storeImageSrc(file.Url);\n      setImageSrc(file.Url);\n      if (isReplaceImage) {\n        replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\n          altText: file.FileName,\n          id: imageAnchorEl.buttonId,\n          url: file.Url,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n        setReplaceImage(false);\n      } else {\n        uploadImage(imageAnchorEl.containerId, {\n          altText: file.FileName,\n          id: crypto.randomUUID(),\n          // Use existing ID\n          url: file.Url,\n          // Directly use the URL\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      }\n    }\n    setModelOpen(false);\n  };\n  const handleReplaceImage = event => {\n    var _event$target$files3;\n    const file = (_event$target$files3 = event.target.files) === null || _event$target$files3 === void 0 ? void 0 : _event$target$files3[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\n          altText: file.name,\n          id: imageAnchorEl.buttonId,\n          url: reader.result,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleClick = (event, containerId, imageId, isImage, currentHeight) => {\n    if ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\n    setImageAnchorEl({\n      buttonId: imageId,\n      containerId: containerId,\n      value: event.currentTarget\n    });\n    setSettingsAnchorEl(null);\n    setCurrentImageSectionInfo({\n      currentContainerId: containerId,\n      isImage,\n      height: currentHeight\n    });\n    setShowHyperlinkInput({\n      currentContainerId: \"\",\n      isOpen: false\n    });\n  };\n  const handleClose = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      value: null\n    });\n  };\n  const open = Boolean(imageAnchorEl.value);\n  const colorPickerOpen = Boolean(colorPickerAnchorEl);\n  const id = open ? \"image-popover\" : undefined;\n  const handleIncreaseHeight = prevHeight => {\n    if (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\n    const newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const handleDecreaseHeight = prevHeight => {\n    if (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\n    const newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const triggerImageUpload = () => {\n    var _document$getElementB;\n    (_document$getElementB = document.getElementById(\"replace-upload\")) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.click();\n  };\n  const currentContainerColor = ((_imagesContainer$find = imagesContainer.find(item => item.id === imageAnchorEl.containerId)) === null || _imagesContainer$find === void 0 ? void 0 : _imagesContainer$find.style.backgroundColor) || \"transparent\";\n  const handleDeleteSection = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      value: null\n    });\n    deleteImageContainer(imageAnchorEl.containerId);\n    if (onDelete) {\n      onDelete();\n    }\n  };\n  const handleLinkSubmit = event => {\n    if (event.key === \"Enter\" && imageLink) {\n      uploadImage(imageAnchorEl.containerId, {\n        altText: \"New Image\",\n        id: crypto.randomUUID(),\n        url: imageLink,\n        backgroundColor: \"transparent\",\n        objectFit: IMG_OBJECT_FIT\n      });\n      setShowHyperlinkInput({\n        currentContainerId: \"\",\n        isOpen: false\n      });\n    }\n  };\n  const handleCloneImgContainer = () => {\n    if (isCloneDisabled) {\n      return;\n    }\n    cloneImageContainer(imageAnchorEl.containerId);\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleCloseColorPicker = () => {\n    setColorPickerAnchorEl(null);\n  };\n  const handleColorChange = color => {\n    setSelectedColor(color.hex);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      backgroundColor: color.hex\n    });\n  };\n  const handleBackgroundColorClick = event => {\n    setColorPickerAnchorEl(event.currentTarget);\n  };\n  const getGuidePopupPosition = () => {\n    const dialogElement = document.querySelector('.qadpt-guide-popup .MuiDialog-paper');\n    if (dialogElement) {\n      const rect = dialogElement.getBoundingClientRect();\n      return {\n        top: rect.top,\n        left: rect.left,\n        width: rect.width,\n        height: rect.height\n      };\n    }\n    const guidePopupElement = document.getElementById('guide-popup');\n    if (guidePopupElement) {\n      const rect = guidePopupElement.getBoundingClientRect();\n      return {\n        top: rect.top,\n        left: rect.left,\n        width: rect.width,\n        height: rect.height\n      };\n    }\n    return null;\n  };\n  const getImagePopoverPosition = () => {\n    const guidePopupPos = getGuidePopupPosition();\n    if (guidePopupPos) {\n      const popoverHeight = 40;\n      const requiredGap = 10;\n      const minTopMargin = 8;\n      const viewportHeight = window.innerHeight;\n      const actualGuidePopupPos = guidePopupPos;\n      let topPosition;\n      const positionWithGap = actualGuidePopupPos.top - popoverHeight - requiredGap;\n      if (positionWithGap >= minTopMargin) {\n        topPosition = positionWithGap;\n      } else {\n        const availableSpaceAbove = actualGuidePopupPos.top - popoverHeight - minTopMargin;\n        if (availableSpaceAbove >= 15) {\n          topPosition = positionWithGap;\n        } else if (availableSpaceAbove >= 10) {\n          topPosition = actualGuidePopupPos.top - popoverHeight - 10;\n        } else if (availableSpaceAbove >= 5) {\n          topPosition = actualGuidePopupPos.top - popoverHeight - 5;\n        } else {\n          topPosition = Math.max(minTopMargin, actualGuidePopupPos.top - popoverHeight - 2);\n        }\n      }\n      const maxTopPosition = viewportHeight - popoverHeight;\n      topPosition = Math.min(topPosition, maxTopPosition);\n      let leftPosition = guidePopupPos.left + guidePopupPos.width / 2 - 250;\n      if (leftPosition < 10) {\n        leftPosition = 10;\n      }\n      const popoverWidth = 500;\n      const viewportWidth = window.innerWidth;\n      if (leftPosition + popoverWidth > viewportWidth - 10) {\n        leftPosition = viewportWidth - popoverWidth - 10;\n      }\n      return {\n        top: topPosition,\n        left: leftPosition,\n        position: 'fixed',\n        zIndex: 999999\n      };\n    }\n    return {};\n  };\n  const getSettingsPopoverPosition = () => {\n    const guidePopupPos = getGuidePopupPosition();\n    if (guidePopupPos) {\n      const viewportHeight = window.innerHeight;\n      const viewportWidth = window.innerWidth;\n      const settingsPopupHeight = 200;\n      const settingsPopupWidth = 300;\n      const actualGuidePopupPos = guidePopupPos;\n      let leftPosition = actualGuidePopupPos.left + actualGuidePopupPos.width + 10;\n      let topPosition = actualGuidePopupPos.top + actualGuidePopupPos.height / 2 - settingsPopupHeight / 2 + 10;\n      if (leftPosition + settingsPopupWidth > viewportWidth - 10) {\n        leftPosition = actualGuidePopupPos.left - settingsPopupWidth - 10;\n      }\n      if (topPosition < 10) {\n        topPosition = 10;\n      } else if (topPosition + settingsPopupHeight > viewportHeight - 10) {\n        topPosition = viewportHeight - settingsPopupHeight - 10;\n      }\n      leftPosition = Math.max(10, Math.min(leftPosition, viewportWidth - settingsPopupWidth - 10));\n      return {\n        top: topPosition,\n        left: leftPosition,\n        position: 'fixed',\n        zIndex: 999999\n      };\n    }\n    return {};\n  };\n  const updatePopoverPositions = () => {\n    setPopoverPositions({\n      imagePopover: getImagePopoverPosition(),\n      settingsPopover: getSettingsPopoverPosition()\n    });\n  };\n  useEffect(() => {\n    const handleResize = () => {\n      updatePopoverPositions();\n    };\n    const handleScroll = () => {\n      updatePopoverPositions();\n    };\n    if (open || openSettingsPopover) {\n      updatePopoverPositions();\n      const positionCheckInterval = setInterval(() => {\n        if (open || openSettingsPopover) {\n          updatePopoverPositions();\n        } else {\n          clearInterval(positionCheckInterval);\n        }\n      }, 200);\n      window.qadptPositionCheckInterval = positionCheckInterval;\n    }\n    window.addEventListener('resize', handleResize);\n    window.addEventListener('scroll', handleScroll);\n    const perfectScrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\n    if (perfectScrollbarElement) {\n      perfectScrollbarElement.addEventListener('ps-scroll-y', handleScroll);\n      perfectScrollbarElement.addEventListener('scroll', handleScroll);\n    }\n    const guidePopupElement = document.getElementById('guide-popup');\n    const dialogElement = document.querySelector('.qadpt-guide-popup .MuiDialog-paper');\n    const scrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\n    let resizeObserver = null;\n    let mutationObserver = null;\n    if (window.ResizeObserver) {\n      resizeObserver = new ResizeObserver(entries => {\n        let hasSignificantChange = false;\n        entries.forEach(entry => {\n          const {\n            width,\n            height\n          } = entry.contentRect;\n          const element = entry.target;\n          const lastWidth = parseFloat(element.dataset.lastWidth || '0');\n          const lastHeight = parseFloat(element.dataset.lastHeight || '0');\n          if (Math.abs(width - lastWidth) > 1 || Math.abs(height - lastHeight) > 1) {\n            hasSignificantChange = true;\n            element.dataset.lastWidth = width.toString();\n            element.dataset.lastHeight = height.toString();\n          }\n        });\n        if (hasSignificantChange) {\n          updatePopoverPositions();\n          setTimeout(() => {\n            updatePopoverPositions();\n          }, 50);\n        }\n      });\n      if (guidePopupElement) {\n        resizeObserver.observe(guidePopupElement);\n      }\n      if (dialogElement) {\n        resizeObserver.observe(dialogElement);\n      }\n      if (scrollbarElement) {\n        resizeObserver.observe(scrollbarElement);\n      }\n    }\n    if (guidePopupElement && window.MutationObserver) {\n      mutationObserver = new MutationObserver(mutations => {\n        let shouldUpdate = false;\n        mutations.forEach(mutation => {\n          if (mutation.type === 'childList' || mutation.type === 'attributes' && ['style', 'class'].includes(mutation.attributeName || '')) {\n            shouldUpdate = true;\n          }\n        });\n        if (shouldUpdate) {\n          setTimeout(() => {\n            updatePopoverPositions();\n          }, 50);\n        }\n      });\n      mutationObserver.observe(guidePopupElement, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: ['style', 'class']\n      });\n    }\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      window.removeEventListener('scroll', handleScroll);\n      const perfectScrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\n      if (perfectScrollbarElement) {\n        perfectScrollbarElement.removeEventListener('ps-scroll-y', handleScroll);\n        perfectScrollbarElement.removeEventListener('scroll', handleScroll);\n      }\n      if (window.qadptPositionCheckInterval) {\n        clearInterval(window.qadptPositionCheckInterval);\n        delete window.qadptPositionCheckInterval;\n      }\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n      if (mutationObserver) {\n        mutationObserver.disconnect();\n      }\n    };\n  }, [open, openSettingsPopover]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [imagesContainer.map(item => {\n      var _item$images$, _item$images$2, _item$images$3, _item$style;\n      const imageSrc = (_item$images$ = item.images[0]) === null || _item$images$ === void 0 ? void 0 : _item$images$.url;\n      const imageId = (_item$images$2 = item.images[0]) === null || _item$images$2 === void 0 ? void 0 : _item$images$2.id;\n      const objectFit = ((_item$images$3 = item.images[0]) === null || _item$images$3 === void 0 ? void 0 : _item$images$3.objectFit) || IMG_OBJECT_FIT;\n      const currentSecHeight = (item === null || item === void 0 ? void 0 : (_item$style = item.style) === null || _item$style === void 0 ? void 0 : _item$style.height) || IMG_CONTAINER_DEFAULT_HEIGHT;\n      const id = item.id;\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: \"100%\",\n          height: \"100%\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          justifyContent: \"flex-start\",\n          alignItems: \"center\",\n          margin: \"0px\",\n          overflow: \"auto\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            ...imageContainerStyle,\n            backgroundColor: item.style.backgroundColor,\n            height: `${item.style.height}px`\n          },\n          onClick: e => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight),\n          component: \"div\",\n          id: id,\n          onMouseOver: () => {\n            setImageAnchorEl({\n              buttonId: imageId,\n              containerId: id,\n              value: null\n            });\n          },\n          children: imageSrc ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: imageSrc,\n            alt: \"Uploaded\",\n            style: {\n              ...imageStyle,\n              objectFit\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 9\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: \"center\",\n              width: \"100%\",\n              height: \"100%\",\n              display: \"flex\",\n              flexDirection: \"column\",\n              justifyContent: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: iconTextStyle,\n              component: \"div\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: uploadfile\n                },\n                style: {\n                  display: \"inline-block\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                align: \"center\",\n                sx: {\n                  fontSize: \"14px\",\n                  fontWeight: \"600\"\n                },\n                children: translate(\"Upload file\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              align: \"center\",\n              color: \"textSecondary\",\n              sx: {\n                fontSize: \"14px\"\n              },\n              children: translate(\"Drag & Drop to upload file\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              align: \"center\",\n              color: \"textSecondary\",\n              sx: {\n                marginTop: \"8px\",\n                fontSize: \"14px\"\n              },\n              children: translate(\"Or\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 10\n            }, this), showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? /*#__PURE__*/_jsxDEV(TextField, {\n              value: imageLink,\n              onChange: e => setImageLink(e.target.value),\n              onKeyDown: handleLinkSubmit,\n              autoFocus: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 11\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: iconRowStyle,\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: translate(\"Coming soon\"),\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    pointerEvents: \"auto\",\n                    cursor: \"pointer\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: hyperlink\n                    },\n                    style: {\n                      color: \"black\",\n                      cursor: \"pointer\",\n                      fontSize: \"32px\",\n                      opacity: \"0.5\",\n                      pointerEvents: \"none\"\n                    },\n                    id: \"hyperlink\",\n                    className: \"qadpt-image-upload\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 5\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 3\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: translate(\"Coming soon\"),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  onClick: event => {},\n                  dangerouslySetInnerHTML: {\n                    __html: files\n                  },\n                  style: {\n                    color: \"black\",\n                    cursor: \"pointer\",\n                    fontSize: \"32px\",\n                    opacity: \"0.5\"\n                  },\n                  id: \"folder\",\n                  className: \"qadpt-image-upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 751,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: translate(\"Upload File\"),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  onClick: event => {\n                    var _document$getElementB2;\n                    event === null || event === void 0 ? void 0 : event.stopPropagation();\n                    (_document$getElementB2 = document.getElementById(\"file-upload\")) === null || _document$getElementB2 === void 0 ? void 0 : _document$getElementB2.click();\n                  },\n                  id: \"file-upload1\",\n                  className: \"qadpt-image-upload\",\n                  dangerouslySetInnerHTML: {\n                    __html: uploadicon\n                  },\n                  style: {\n                    color: \"black\",\n                    cursor: \"pointer\",\n                    fontSize: \"32px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 12\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"file-upload\",\n                style: {\n                  display: \"none\"\n                },\n                accept: \"image/*\",\n                onChange: handleImageUpload\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 12\n              }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n                open: snackbarOpen,\n                autoHideDuration: 3000,\n                onClose: closeSnackbar,\n                anchorOrigin: {\n                  vertical: 'bottom',\n                  horizontal: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(Alert, {\n                  onClose: closeSnackbar,\n                  severity: snackbarSeverity,\n                  sx: {\n                    width: '100%'\n                  },\n                  children: snackbarMessage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 13\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 12\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 7\n        }, this)\n      }, id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 6\n      }, this);\n    }), Boolean(imageAnchorEl.value) ? /*#__PURE__*/_jsxDEV(Popover, {\n      className: \"qadpt-imgsec-popover\",\n      id: id,\n      open: open,\n      anchorEl: null,\n      onClose: handleClose,\n      anchorReference: \"none\",\n      slotProps: {\n        paper: {\n          style: {\n            ...popoverPositions.imagePopover,\n            height: 'auto',\n            width: 'auto',\n            padding: '5px 10px',\n            marginLeft: 'auto',\n            marginRight: 'auto'\n          }\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"15px\",\n          height: \"100%\",\n          padding: \"0 10px\",\n          fontSize: \"12px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\"\n          },\n          children: currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId && currentImageSectionInfo.isImage ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: replaceimageicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              fontSize: \"12px\",\n              marginLeft: \"5px\",\n              onClick: triggerImageUpload,\n              children: translate(\"Replace Image\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"replace-upload\",\n              style: {\n                display: \"none\"\n              },\n              accept: \"image/*\",\n              onChange: handleReplaceImage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 10\n            }, this)]\n          }, void 0, true) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 826,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-tool-items\",\n          sx: {\n            display: \"flex\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: sectionheight\n            },\n            style: {\n              display: \"flex\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\") : translate(\"Decrease height\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleDecreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 866,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            fontSize: \"12px\",\n            children: currentImageSectionInfo.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\") : translate(\"Increase height\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleIncreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 882,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 849,\n          columnNumber: 1\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Settings\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-tool-items\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: handleSettingsClick,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: Settings\n                  },\n                  style: {\n                    color: \"black\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 894,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 890,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Popover, {\n              className: \"qadpt-imgset\",\n              open: openSettingsPopover,\n              anchorEl: null,\n              onClose: handleCloseSettingsPopover,\n              anchorReference: \"none\",\n              slotProps: {\n                paper: {\n                  style: {\n                    ...popoverPositions.settingsPopover,\n                    width: \"205px\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                p: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    sx: {\n                      color: \"rgba(95, 158, 160, 1)\"\n                    },\n                    children: translate(\"Image Properties\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 924,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleCloseSettingsPopover,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: CrossIcon\n                      },\n                      style: {\n                        color: \"black\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 934,\n                      columnNumber: 12\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate(\"Coming soon\"),\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    mt: 2,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      sx: {\n                        marginBottom: \"10px\"\n                      },\n                      children: translate(\"Image Actions\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 942,\n                      columnNumber: 11\n                    }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                      select: true,\n                      fullWidth: true,\n                      variant: \"outlined\",\n                      size: \"small\",\n                      value: selectedAction,\n                      onChange: handleActionChange,\n                      sx: {\n                        \"& .MuiOutlinedInput-root\": {\n                          borderColor: \"rgba(246, 238, 238, 1)\"\n                        }\n                      },\n                      disabled: true,\n                      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"none\",\n                        children: translate(\"None\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 963,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"specificStep\",\n                        children: translate(\"Specific Step\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 964,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"openUrl\",\n                        children: translate(\"Open URL\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 965,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"clickElement\",\n                        children: translate(\"Click Element\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 966,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startTour\",\n                        children: translate(\"Start Tour\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 967,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startMicroSurvey\",\n                        children: translate(\"Start Micro Survey\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 968,\n                        columnNumber: 14\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 949,\n                      columnNumber: 11\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 10\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 940,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: translate(\"Image Formatting\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 973,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    gap: 1,\n                    mt: 1,\n                    children: [\"Fill\", \"Fit\"].map(item => {\n                      const currentContainer = imagesContainer.find(c => c.id === imageAnchorEl.containerId);\n                      const currentImage = currentContainer === null || currentContainer === void 0 ? void 0 : currentContainer.images.find(img => img.id === imageAnchorEl.buttonId);\n                      const currentObjectFit = (currentImage === null || currentImage === void 0 ? void 0 : currentImage.objectFit) || IMG_OBJECT_FIT;\n                      const isSelected = item === \"Fill\" && currentObjectFit === \"cover\" || item === \"Fit\" && currentObjectFit === \"contain\";\n                      return /*#__PURE__*/_jsxDEV(Button, {\n                        onClick: () => toggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item),\n                        variant: \"outlined\",\n                        size: \"small\",\n                        sx: {\n                          width: \"88.5px\",\n                          height: \"41px\",\n                          padding: \"10px 12px\",\n                          gap: \"12px\",\n                          borderRadius: \"6px 6px 6px 6px\",\n                          border: isSelected ? \"1px solid rgba(95, 158, 160, 1)\" : \"1px solid rgba(246, 238, 238, 1)\",\n                          backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\n                          backgroundBlendMode: \"multiply\",\n                          color: \"black\",\n                          \"&:hover\": {\n                            backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\"\n                          }\n                        },\n                        children: translate(item)\n                      }, item, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 995,\n                        columnNumber: 14\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 979,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 888,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 887,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Background Color\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleBackgroundColorClick,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  backgroundColor: selectedColor,\n                  borderRadius: \"100%\",\n                  width: \"20px\",\n                  height: \"20px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1038,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1033,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1032,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\") : translate(\"Clone Section\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleCloneImgContainer,\n              size: \"small\",\n              disabled: isCloneDisabled,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1055,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1050,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1049,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1048,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Delete Section\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleDeleteSection,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1070,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1066,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1065,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1063,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 796,\n      columnNumber: 5\n    }, this) : null, isModelOpen && /*#__PURE__*/_jsxDEV(SelectImageFromApplication, {\n      isOpen: isModelOpen,\n      handleModelClose: () => setModelOpen(false),\n      onImageSelect: handleImageUploadFormApp,\n      setFormOfUpload: setFormOfUpload,\n      formOfUpload: formOfUpload,\n      handleReplaceImage: handleReplaceImage,\n      isReplaceImage: isReplaceImage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1079,\n      columnNumber: 6\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      open: colorPickerOpen,\n      anchorEl: colorPickerAnchorEl,\n      onClose: handleCloseColorPicker,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(ChromePicker, {\n          color: currentContainerColor,\n          onChange: handleColorChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1096,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1100,\n          columnNumber: 8\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1095,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1082,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(ImageSection, \"phKmd8/vYJ2if4XJ7NWyWxDuMKo=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ImageSection;\nexport default ImageSection;\nvar _c;\n$RefreshReg$(_c, \"ImageSection\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "Typography", "Popover", "IconButton", "TextField", "MenuItem", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "RemoveIcon", "AddIcon", "useTranslation", "uploadfile", "hyperlink", "files", "uploadicon", "replaceimageicon", "copyicon", "deleteicon", "sectionheight", "Settings", "CrossIcon", "useDrawerStore", "IMG_CONTAINER_DEFAULT_HEIGHT", "IMG_CONTAINER_MAX_HEIGHT", "IMG_CONTAINER_MIN_HEIGHT", "IMG_OBJECT_FIT", "IMG_STEP_VALUE", "ChromePicker", "SelectImageFromApplication", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageSection", "setImageSrc", "setImageName", "onDelete", "onClone", "isCloneDisabled", "_s", "_imagesContainer$find", "t", "translate", "uploadImage", "imagesContainer", "imageAnchorEl", "setImageAnchorEl", "replaceImage", "cloneImageContainer", "deleteImageContainer", "updateImageContainer", "toggleFit", "storeImageSrc", "state", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "snackbarKey", "setSnackbarKey", "openSnackbar", "prev", "closeSnackbar", "showHyperlinkInput", "setShowHyperlinkInput", "currentContainerId", "isOpen", "imageLink", "setImageLink", "colorPickerAnchorEl", "setColorPickerAnchorEl", "currentImageSectionInfo", "setCurrentImageSectionInfo", "isImage", "height", "selectedAction", "setSelectedAction", "isModelOpen", "setModelOpen", "formOfUpload", "setFormOfUpload", "settingsAnchorEl", "setSettingsAnchorEl", "selectedColor", "setSelectedColor", "isReplaceImage", "setReplaceImage", "guidePopupRef", "popoverPositions", "setPopoverPositions", "imagePopover", "settingsPopover", "openSettingsPopover", "Boolean", "handleActionChange", "event", "target", "value", "handleSettingsClick", "currentTarget", "handleCloseSettingsPopover", "imageContainerStyle", "width", "display", "justifyContent", "alignItems", "padding", "margin", "overflow", "imageStyle", "borderRadius", "iconRowStyle", "gap", "marginTop", "iconTextStyle", "flexDirection", "handleImageUpload", "_event$target$files", "file", "_event$target$files2", "parts", "name", "split", "extension", "pop", "length", "reader", "FileReader", "onloadend", "base64Image", "result", "containerId", "altText", "id", "crypto", "randomUUID", "url", "backgroundColor", "objectFit", "readAsDataURL", "handleImageUploadFormApp", "Url", "buttonId", "FileName", "handleReplaceImage", "_event$target$files3", "handleClick", "imageId", "currentHeight", "includes", "handleClose", "open", "colorPickerOpen", "undefined", "handleIncreaseHeight", "prevHeight", "newHeight", "Math", "min", "handleDecreaseHeight", "max", "triggerImageUpload", "_document$getElementB", "document", "getElementById", "click", "currentContainerColor", "find", "item", "style", "handleDeleteSection", "handleLinkSubmit", "key", "handleCloneImgContainer", "handleCloseColorPicker", "handleColorChange", "color", "hex", "handleBackgroundColorClick", "getGuidePopupPosition", "dialogElement", "querySelector", "rect", "getBoundingClientRect", "top", "left", "guidePopupElement", "getImagePopoverPosition", "guidePopupPos", "popoverHeight", "requiredGap", "minTopMargin", "viewportHeight", "window", "innerHeight", "actualGuidePopupPos", "topPosition", "positionWithGap", "availableSpaceAbove", "maxTopPosition", "leftPosition", "popoverWidth", "viewportWidth", "innerWidth", "position", "zIndex", "getSettingsPopoverPosition", "settingsPopupHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatePopoverPositions", "handleResize", "handleScroll", "positionCheckInterval", "setInterval", "clearInterval", "qadptPositionCheckInterval", "addEventListener", "perfectScrollbarElement", "scrollbarElement", "resizeObserver", "mutationObserver", "ResizeObserver", "entries", "hasSignificantChange", "for<PERSON>ach", "entry", "contentRect", "element", "lastWidth", "parseFloat", "dataset", "lastHeight", "abs", "toString", "setTimeout", "observe", "MutationObserver", "mutations", "shouldUpdate", "mutation", "type", "attributeName", "childList", "subtree", "attributes", "attributeFilter", "removeEventListener", "disconnect", "children", "map", "_item$images$", "_item$images$2", "_item$images$3", "_item$style", "imageSrc", "images", "currentSecHeight", "sx", "onClick", "e", "component", "onMouseOver", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textAlign", "dangerouslySetInnerHTML", "__html", "variant", "align", "fontSize", "fontWeight", "onChange", "onKeyDown", "autoFocus", "title", "pointerEvents", "cursor", "opacity", "className", "_document$getElementB2", "stopPropagation", "accept", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "anchorEl", "anchorReference", "slotProps", "paper", "marginLeft", "marginRight", "size", "disabled", "p", "mt", "marginBottom", "select", "fullWidth", "borderColor", "currentC<PERSON><PERSON>", "c", "currentImage", "img", "currentObjectFit", "isSelected", "border", "backgroundBlendMode", "handleModelClose", "onImageSelect", "transform<PERSON><PERSON>in", "_c", "$RefreshReg$"], "sources": ["E:/Code/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/Imagesection.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport {\r\n\t<PERSON>,\r\n\tTypo<PERSON>,\r\n\tPopover,\r\n\tIconButton,\r\n\tTextField,\r\n\tMenuItem,\r\n\tButton,\r\n\tTooltip,\r\n\tSnackbar,\r\n\tAlert\r\n} from \"@mui/material\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport { FileUpload } from \"../../../models/FileUpload\";\r\n\r\nimport {\r\n\tuploadfile,\r\n\thyperlink,\r\n\tfiles,\r\n\tuploadicon,\r\n\treplaceimageicon,\r\n\tcopyicon,\r\n\tdeleteicon,\r\n\tsectionheight,\r\n\tSettings,\r\n\tCrossIcon,\r\n} from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, {\r\n\tIMG_CONTAINER_DEFAULT_HEIGHT,\r\n\tIMG_CONTAINER_MAX_HEIGHT,\r\n\tIMG_CONTAINER_MIN_HEIGHT,\r\n\tIMG_OBJECT_FIT,\r\n\tIMG_STEP_VALUE,\r\n} from \"../../../store/drawerStore\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport \"./PopupSections.css\";\r\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\r\n\r\nconst ImageSection = ({ setImageSrc, setImageName, onDelete, onClone, isCloneDisabled }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tuploadImage,\r\n\t\timagesContainer,\r\n\t\timageAnchorEl,\r\n\t\tsetImageAnchorEl,\r\n\t\treplaceImage,\r\n\t\tcloneImageContainer,\r\n\t\tdeleteImageContainer,\r\n\t\tupdateImageContainer,\r\n\t\ttoggleFit,\r\n\t\tsetImageSrc: storeImageSrc,\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\tconst [snackbarMessage, setSnackbarMessage] = useState('');\r\n\tconst [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('info');\r\n\r\n\tconst [snackbarKey, setSnackbarKey] = useState<number>(0); \r\n\r\n\tconst openSnackbar = () => {\r\n\t\tsetSnackbarKey(prev => prev + 1);\r\n\t\tsetSnackbarOpen(true);\r\n\t};\r\n\tconst closeSnackbar = () => {\r\n\t\tsetSnackbarOpen(false);\r\n\t};\r\n\tconst [showHyperlinkInput, setShowHyperlinkInput] = useState<{ currentContainerId: string; isOpen: boolean }>({\r\n\t\tcurrentContainerId: \"\",\r\n\t\tisOpen: false,\r\n\t});\r\n\tconst [imageLink, setImageLink] = useState<string>(\"\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [currentImageSectionInfo, setCurrentImageSectionInfo] = useState<{\r\n\t\tcurrentContainerId: string;\r\n\t\tisImage: boolean;\r\n\t\theight: number;\r\n\t}>({ currentContainerId: \"\", isImage: false, height: IMG_CONTAINER_DEFAULT_HEIGHT });\r\n\r\n\tconst [selectedAction, setSelectedAction] = useState(\"none\");\r\n\tconst [isModelOpen, setModelOpen] = useState(false);\r\n\tconst [formOfUpload, setFormOfUpload] = useState<String>(\"\");\r\n\tconst [settingsAnchorEl, setSettingsAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [selectedColor, setSelectedColor] = useState<string>(\"#313030\");\r\n\tconst [isReplaceImage, setReplaceImage] = useState(false);\r\n\tconst guidePopupRef = useRef<HTMLElement | null>(null);\r\n\tconst [popoverPositions, setPopoverPositions] = useState({\r\n\t\timagePopover: {},\r\n\t\tsettingsPopover: {}\r\n\t});\r\n\r\n\r\n\tconst openSettingsPopover = Boolean(settingsAnchorEl);\r\n\r\n\tconst handleActionChange = (event: any) => {\r\n\t\tsetSelectedAction(event.target.value);\r\n\t};\r\n\r\n\tconst handleSettingsClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSettingsAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleCloseSettingsPopover = () => {\r\n\t\tsetSettingsAnchorEl(null);\r\n\t};\r\n\tconst imageContainerStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\talignItems: \"center\",\r\n\t\tpadding: 0,\r\n\t\tmargin: 0,\r\n\t\toverflow: \"hidden\",\r\n\t};\r\n\r\n\tconst imageStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tmargin: 0,\r\n\t\tpadding: 0,\r\n\t\tborderRadius: \"0\",\r\n\t};\r\n\r\n\tconst iconRowStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tgap: \"16px\",\r\n\t\tmarginTop: \"10px\",\r\n\t};\r\n\r\n\tconst iconTextStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\twidth: \"100%\",\r\n\t};\r\n\r\n\tconst handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst parts = file.name.split('.');\r\n   \t\t\tconst extension = parts.pop();\r\n\r\n\r\n   \t\t\t if (parts.length > 1 || !extension ) {\r\n\t\t\t  setSnackbarMessage(\"Uploaded file name should not contain any special character\");\r\n       \t\t setSnackbarSeverity(\"error\");\r\n\t\t\t setSnackbarOpen(true);\r\n\t\t\t event.target.value = '';\r\n      \t\t return;\r\n\t\t\t \r\n   \t\t\t }\r\n\t\t\t if(file.name.length > 128){\r\n\t\t\t\tsetSnackbarMessage(\"File name should not exceed 128 characters\");\r\n       \t\t\tsetSnackbarSeverity(\"error\");\r\n\t\t\t \tsetSnackbarOpen(true);\r\n\t\t\t \tevent.target.value = '';\r\n      \t\t \treturn;\r\n\t\t\t }\r\n\t\t\tsetImageName(event.target.files?.[0].name);\r\n\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\tconst base64Image = reader.result as string;\r\n\t\t\t\tstoreImageSrc(base64Image);\r\n\t\t\t\tsetImageSrc(base64Image);\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\turl: base64Image,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\r\n\tconst handleImageUploadFormApp = (file: FileUpload) => {\r\n\t\tif (file) {\r\n\t\t\tstoreImageSrc(file.Url);\r\n\t\t\tsetImageSrc(file.Url);\r\n\t\t\tif (isReplaceImage) {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: file.Url,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t\tsetReplaceImage(false);\r\n\t\t\t} else {\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: crypto.randomUUID(), // Use existing ID\r\n\t\t\t\t\turl: file.Url, // Directly use the URL\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\tconst handleReplaceImage = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: reader.result,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClick = (\r\n\t\tevent: React.MouseEvent<HTMLElement>,\r\n\t\tcontainerId: string,\r\n\t\timageId: string,\r\n\t\tisImage: boolean,\r\n\t\tcurrentHeight: number\r\n\t) => {\r\n\r\n\t\tif ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: imageId,\r\n\t\t\tcontainerId: containerId,\r\n\r\n\t\t\tvalue: event.currentTarget,\r\n\t\t});\r\n\t\tsetSettingsAnchorEl(null);\r\n\t\tsetCurrentImageSectionInfo({\r\n\t\t\tcurrentContainerId: containerId,\r\n\t\t\tisImage,\r\n\t\t\theight: currentHeight,\r\n\t\t});\r\n\t\tsetShowHyperlinkInput({\r\n\t\t\tcurrentContainerId: \"\",\r\n\t\t\tisOpen: false,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t};\r\n\r\n\tconst open = Boolean(imageAnchorEl.value);\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\r\n\tconst id = open ? \"image-popover\" : undefined;\r\n\r\n\tconst handleIncreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\r\n\t\tconst newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst handleDecreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\r\n\t\tconst newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst triggerImageUpload = () => {\r\n\t\tdocument.getElementById(\"replace-upload\")?.click();\r\n\r\n\t};\r\n\r\n\tconst currentContainerColor =\r\n\t\timagesContainer.find((item) => item.id === imageAnchorEl.containerId)?.style.backgroundColor || \"transparent\";\r\n\r\n\tconst handleDeleteSection = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\r\n\r\n\t\tdeleteImageContainer(imageAnchorEl.containerId);\r\n\r\n\r\n\t\tif (onDelete) {\r\n\t\t\tonDelete();\r\n\t\t}\r\n\t};\r\n\r\n\r\n\tconst handleLinkSubmit = (event: React.KeyboardEvent<HTMLInputElement>) => {\r\n\t\tif (event.key === \"Enter\" && imageLink) {\r\n\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\taltText: \"New Image\",\r\n\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\turl: imageLink,\r\n\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t});\r\n\t\t\tsetShowHyperlinkInput({\r\n\t\t\t\tcurrentContainerId: \"\",\r\n\t\t\t\tisOpen: false,\r\n\t\t\t});\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloneImgContainer = () => {\r\n\r\n\t\tif (isCloneDisabled) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\r\n\t\tcloneImageContainer(imageAnchorEl.containerId);\r\n\r\n\r\n\t\tif (onClone) {\r\n\t\t\tonClone();\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\tsetSelectedColor(color.hex);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\r\n\tconst getGuidePopupPosition = () => {\r\n\r\n\t\tconst dialogElement = document.querySelector('.qadpt-guide-popup .MuiDialog-paper');\r\n\t\tif (dialogElement) {\r\n\t\t\tconst rect = dialogElement.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top,\r\n\t\t\t\tleft: rect.left,\r\n\t\t\t\twidth: rect.width,\r\n\t\t\t\theight: rect.height\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\tconst guidePopupElement = document.getElementById('guide-popup');\r\n\t\tif (guidePopupElement) {\r\n\t\t\tconst rect = guidePopupElement.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top,\r\n\t\t\t\tleft: rect.left,\r\n\t\t\t\twidth: rect.width,\r\n\t\t\t\theight: rect.height\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\r\n\r\n\tconst getImagePopoverPosition = () => {\r\n\t\tconst guidePopupPos = getGuidePopupPosition();\r\n\t\tif (guidePopupPos) {\r\n\t\t\tconst popoverHeight = 40;\r\n\t\t\tconst requiredGap = 10;\r\n\t\t\tconst minTopMargin = 8;\r\n\t\t\tconst viewportHeight = window.innerHeight;\r\n\r\n\r\n\t\t\tconst actualGuidePopupPos = guidePopupPos;\r\n\r\n\t\t\tlet topPosition;\r\n\r\n\r\n\t\t\tconst positionWithGap = actualGuidePopupPos.top - popoverHeight - requiredGap;\r\n\r\n\r\n\t\t\tif (positionWithGap >= minTopMargin) {\r\n\r\n\t\t\t\ttopPosition = positionWithGap;\r\n\t\t\t} else {\r\n\r\n\t\t\t\tconst availableSpaceAbove = actualGuidePopupPos.top - popoverHeight - minTopMargin;\r\n\r\n\t\t\t\tif (availableSpaceAbove >= 15) {\r\n\r\n\t\t\t\t\ttopPosition = positionWithGap;\r\n\t\t\t\t} else if (availableSpaceAbove >= 10) {\r\n\r\n\t\t\t\t\ttopPosition = actualGuidePopupPos.top - popoverHeight - 10;\r\n\t\t\t\t} else if (availableSpaceAbove >= 5) {\r\n\r\n\t\t\t\t\ttopPosition = actualGuidePopupPos.top - popoverHeight - 5;\r\n\t\t\t\t} else {\r\n\r\n\t\t\t\t\ttopPosition = Math.max(minTopMargin, actualGuidePopupPos.top - popoverHeight - 2);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\t\t\tconst maxTopPosition = viewportHeight - popoverHeight;\r\n\t\t\ttopPosition = Math.min(topPosition, maxTopPosition);\r\n\r\n\r\n\t\t\tlet leftPosition = guidePopupPos.left + (guidePopupPos.width / 2) - 250;\r\n\r\n\r\n\t\t\tif (leftPosition < 10) {\r\n\t\t\t\tleftPosition = 10;\r\n\t\t\t}\r\n\r\n\t\t\tconst popoverWidth = 500;\r\n\t\t\tconst viewportWidth = window.innerWidth;\r\n\t\t\tif (leftPosition + popoverWidth > viewportWidth - 10) {\r\n\t\t\t\tleftPosition = viewportWidth - popoverWidth - 10;\r\n\t\t\t}\r\n\r\n\t\t\treturn {\r\n\t\t\t\ttop: topPosition,\r\n\t\t\t\tleft: leftPosition,\r\n\t\t\t\tposition: 'fixed' as const,\r\n\t\t\t\tzIndex: 999999\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn {};\r\n\t};\r\n\r\n\r\n\tconst getSettingsPopoverPosition = () => {\r\n\t\tconst guidePopupPos = getGuidePopupPosition();\r\n\t\tif (guidePopupPos) {\r\n\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\tconst viewportWidth = window.innerWidth;\r\n\t\t\tconst settingsPopupHeight = 200;\r\n\t\t\tconst settingsPopupWidth = 300;\r\n\t\t\tconst actualGuidePopupPos = guidePopupPos;\r\n\t\t\r\n\r\n\t\t\tlet leftPosition = actualGuidePopupPos.left + actualGuidePopupPos.width + 10;\r\n\t\t\tlet topPosition = actualGuidePopupPos.top + (actualGuidePopupPos.height / 2) - (settingsPopupHeight/2) + 10;\r\n\r\n\r\n\t\t\tif (leftPosition + settingsPopupWidth > viewportWidth - 10) {\r\n\t\t\t\tleftPosition = actualGuidePopupPos.left - settingsPopupWidth - 10;\r\n\t\t\t}\r\n\r\n\r\n\t\t\tif (topPosition < 10) {\r\n\t\t\t\ttopPosition = 10;\r\n\t\t\t} else if (topPosition + settingsPopupHeight > viewportHeight - 10) {\r\n\t\t\t\ttopPosition = viewportHeight - settingsPopupHeight - 10;\r\n\t\t\t}\r\n\r\n\r\n\t\t\tleftPosition = Math.max(10, Math.min(leftPosition, viewportWidth - settingsPopupWidth - 10));\r\n\r\n\t\t\treturn {\r\n\t\t\t\ttop: topPosition,\r\n\t\t\t\tleft: leftPosition,\r\n\t\t\t\tposition: 'fixed' as const,\r\n\t\t\t\tzIndex: 999999\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn {};\r\n\t};\r\n\r\n\r\n\tconst updatePopoverPositions = () => {\r\n\t\tsetPopoverPositions({\r\n\t\t\timagePopover: getImagePopoverPosition(),\r\n\t\t\tsettingsPopover: getSettingsPopoverPosition()\r\n\t\t});\r\n\t};\r\n\r\n\r\n\tuseEffect(() => {\r\n\t\tconst handleResize = () => {\r\n\t\t\tupdatePopoverPositions();\r\n\t\t};\r\n\r\n\t\tconst handleScroll = () => {\r\n\t\t\tupdatePopoverPositions();\r\n\t\t};\r\n\r\n\r\n\t\tif (open || openSettingsPopover) {\r\n\t\t\tupdatePopoverPositions();\r\n\r\n\r\n\t\t\tconst positionCheckInterval = setInterval(() => {\r\n\t\t\t\tif (open || openSettingsPopover) {\r\n\t\t\t\t\tupdatePopoverPositions();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tclearInterval(positionCheckInterval);\r\n\t\t\t\t}\r\n\t\t\t}, 200);\r\n\r\n\t\t\t(window as any).qadptPositionCheckInterval = positionCheckInterval;\r\n\t\t}\r\n\r\n\r\n\t\twindow.addEventListener('resize', handleResize);\r\n\t\twindow.addEventListener('scroll', handleScroll);\r\n\r\n\r\n\t\tconst perfectScrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\r\n\t\tif (perfectScrollbarElement) {\r\n\t\t\tperfectScrollbarElement.addEventListener('ps-scroll-y', handleScroll);\r\n\t\t\tperfectScrollbarElement.addEventListener('scroll', handleScroll);\r\n\t\t}\r\n\r\n\r\n\t\tconst guidePopupElement = document.getElementById('guide-popup');\r\n\t\tconst dialogElement = document.querySelector('.qadpt-guide-popup .MuiDialog-paper');\r\n\t\tconst scrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\r\n\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver((entries) => {\r\n\t\t\t\tlet hasSignificantChange = false;\r\n\t\t\t\tentries.forEach((entry) => {\r\n\t\t\t\t\tconst { width, height } = entry.contentRect;\r\n\t\t\t\t\tconst element = entry.target as HTMLElement;\r\n\t\t\t\t\tconst lastWidth = parseFloat(element.dataset.lastWidth || '0');\r\n\t\t\t\t\tconst lastHeight = parseFloat(element.dataset.lastHeight || '0');\r\n\r\n\t\t\t\t\tif (Math.abs(width - lastWidth) > 1 || Math.abs(height - lastHeight) > 1) {\r\n\t\t\t\t\t\thasSignificantChange = true;\r\n\t\t\t\t\t\telement.dataset.lastWidth = width.toString();\r\n\t\t\t\t\t\telement.dataset.lastHeight = height.toString();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t\tif (hasSignificantChange) {\r\n\t\t\t\t\tupdatePopoverPositions();\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tupdatePopoverPositions();\r\n\t\t\t\t\t}, 50);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\r\n\t\t\tif (guidePopupElement) {\r\n\t\t\t\tresizeObserver.observe(guidePopupElement);\r\n\t\t\t}\r\n\t\t\tif (dialogElement) {\r\n\t\t\t\tresizeObserver.observe(dialogElement);\r\n\t\t\t}\r\n\t\t\tif (scrollbarElement) {\r\n\t\t\t\tresizeObserver.observe(scrollbarElement);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t\tif (guidePopupElement && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver((mutations) => {\r\n\t\t\t\tlet shouldUpdate = false;\r\n\t\t\t\tmutations.forEach((mutation) => {\r\n\t\t\t\t\tif (mutation.type === 'childList' ||\r\n\t\t\t\t\t\t(mutation.type === 'attributes' &&\r\n\t\t\t\t\t\t ['style', 'class'].includes(mutation.attributeName || ''))) {\r\n\t\t\t\t\t\tshouldUpdate = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t\tif (shouldUpdate) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tupdatePopoverPositions();\r\n\t\t\t\t\t}, 50);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\tmutationObserver.observe(guidePopupElement, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\twindow.removeEventListener('resize', handleResize);\r\n\t\t\twindow.removeEventListener('scroll', handleScroll);\r\n\r\n\r\n\t\t\tconst perfectScrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\r\n\t\t\tif (perfectScrollbarElement) {\r\n\t\t\t\tperfectScrollbarElement.removeEventListener('ps-scroll-y', handleScroll);\r\n\t\t\t\tperfectScrollbarElement.removeEventListener('scroll', handleScroll);\r\n\t\t\t}\r\n\r\n\r\n\t\t\tif ((window as any).qadptPositionCheckInterval) {\r\n\t\t\t\tclearInterval((window as any).qadptPositionCheckInterval);\r\n\t\t\t\tdelete (window as any).qadptPositionCheckInterval;\r\n\t\t\t}\r\n\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [open, openSettingsPopover]);\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{imagesContainer.map((item) => {\r\n\t\t\t\tconst imageSrc = item.images[0]?.url;\r\n\t\t\t\tconst imageId = item.images[0]?.id;\r\n\t\t\t\tconst objectFit = item.images[0]?.objectFit || IMG_OBJECT_FIT;\r\n\t\t\t\tconst currentSecHeight = (item?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT;\r\n\t\t\t\tconst id = item.id;\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tkey={id}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\r\n\t\t\t\t\t\t\tmargin: \"0px\",\r\n\t\t\t\t\t\t\toverflow: \"auto\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t...imageContainerStyle,\r\n\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor,\r\n\t\t\t\t\t\t\t\theight: `${item.style.height}px`,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={(e) => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight)}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid={id}\r\n\t\t\t\t\t\t\tonMouseOver={() => {\r\n\t\t\t\t\t\t\t\tsetImageAnchorEl({\r\n\t\t\t\t\t\t\t\t\tbuttonId: imageId,\r\n\t\t\t\t\t\t\t\t\tcontainerId: id,\r\n\t\t\t\t\t\t\t\t\tvalue: null,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{imageSrc ? (\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={imageSrc}\r\n\t\t\t\t\t\t\t\t\talt=\"Uploaded\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ ...imageStyle, objectFit }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tsx={iconTextStyle}\r\n\t\t\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadfile }}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"inline-block\" }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"h6\"\r\n\t\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\", fontWeight: \"600\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Upload file\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Drag & Drop to upload file\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ marginTop: \"8px\", fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Or\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t{showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? (\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={imageLink}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setImageLink(e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tonKeyDown={handleLinkSubmit}\r\n\t\t\t\t\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={iconRowStyle}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n  <div style={{ pointerEvents: \"auto\", cursor:\"pointer\"}}>\r\n    <span\r\n      dangerouslySetInnerHTML={{ __html: hyperlink }}\r\n      style={{\r\n        color: \"black\",\r\n        cursor: \"pointer\",\r\n        fontSize: \"32px\",\r\n        opacity: \"0.5\",\r\n        pointerEvents: \"none\",\r\n      }}\r\n      id=\"hyperlink\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n    />\r\n  </div>\r\n</Tooltip>\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: files }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\", opacity: \"0.5\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"folder\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Upload File\")}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tevent?.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdocument.getElementById(\"file-upload\")?.click();\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload1\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleImageUpload}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={closeSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Alert onClose={closeSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{snackbarMessage}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Alert>\r\n\t\t\t\t\t\t\t\t\t\t\t</Snackbar>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t);\r\n\t\t\t})}\r\n\t\t\t{Boolean(imageAnchorEl.value) ? (\r\n\t\t\t\t<Popover\r\n\t\t\t\t\tclassName=\"qadpt-imgsec-popover\"\r\n\t\t\t\t\tid={id}\r\n\t\t\t\t\topen={open}\r\n\t\t\t\t\tanchorEl={null}\r\n\t\t\t\t\tonClose={handleClose}\r\n\t\t\t\t\tanchorReference=\"none\"\r\n\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\t\t...popoverPositions.imagePopover,\r\n\t\t\t\t\t\t\t\theight: 'auto',\r\n\t\t\t\t\t\t\t\twidth: 'auto',\r\n\t\t\t\t\t\t\t\tpadding: '5px 10px',\r\n\t\t\t\t\t\t\t\tmarginLeft: 'auto',\r\n\t\t\t\t\t\t\t\tmarginRight: 'auto',\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tgap: \"15px\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tpadding: \"0 10px\",\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box sx={{ display: \"flex\" }}>\r\n\t\t\t\t\t\t\t{currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId &&\r\n\t\t\t\t\t\t\tcurrentImageSectionInfo.isImage ? (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tfontSize=\"12px\"\r\n\t\t\t\t\t\t\t\t\t\tmarginLeft={\"5px\"}\r\n\t\t\t\t\t\t\t\t\t\tonClick={triggerImageUpload}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Replace Image\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\tid=\"replace-upload\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleReplaceImage}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t</Box>\r\n\r\n<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-tool-items\"\r\n\t\t\t\t\t\t\tsx={{ display: \"flex\", alignItems: \"center\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: sectionheight }}\r\n\t\t\t\t\t\t\tstyle={{ display: \"flex\" }}/>\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\") : translate(\"Decrease height\")}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleDecreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<RemoveIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t<Typography fontSize=\"12px\">{currentImageSectionInfo.height}</Typography>\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\") : translate(\"Increase height\")}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleIncreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<AddIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Settings\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={handleSettingsClick}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Settings }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\"}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-imgset\"\r\n\t\t\t\t\t\t\t\topen={openSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorEl={null}\r\n\t\t\t\t\t\t\t\tonClose={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorReference=\"none\"\r\n\t\t\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\t\t\t\t\t...popoverPositions.settingsPopover,\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"205px\",\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box p={2}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"subtitle1\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ color: \"rgba(95, 158, 160, 1)\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Properties\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: CrossIcon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{ marginBottom: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Actions\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tselect\r\n\t\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={selectedAction}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleActionChange}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"none\">{translate(\"None\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"specificStep\">{translate(\"Specific Step\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"openUrl\">{translate(\"Open URL\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"clickElement\">{translate(\"Click Element\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startTour\">{translate(\"Start Tour\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startMicroSurvey\">{translate(\"Start Micro Survey\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t</TextField>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Formatting\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\tgap={1}\r\n\t\t\t\t\t\t\t\t\t\t\tmt={1}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{[\"Fill\", \"Fit\"].map((item) => {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentContainer = imagesContainer.find((c) => c.id === imageAnchorEl.containerId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentImage = currentContainer?.images.find((img) => img.id === imageAnchorEl.buttonId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentObjectFit = currentImage?.objectFit || IMG_OBJECT_FIT;\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst isSelected = (item === \"Fill\" && currentObjectFit === \"cover\") ||\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  (item === \"Fit\" && currentObjectFit === \"contain\");\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttoggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item as \"Fit\" | \"Fill\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"88.5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"41px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"10px 12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px 6px 6px 6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"1px solid rgba(95, 158, 160, 1)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"1px solid rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundBlendMode: \"multiply\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(item)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Background Color\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: selectedColor,\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"100%\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t}} />\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\") : translate(\"Clone Section\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleCloneImgContainer}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Delete Section\")}>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleDeleteSection}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Popover>\r\n\t\t\t) : null}\r\n\t\t\t{\r\n\t\t\t\tisModelOpen && (\r\n\t\t\t\t\t<SelectImageFromApplication isOpen={isModelOpen} handleModelClose={() => setModelOpen(false)} onImageSelect={handleImageUploadFormApp} setFormOfUpload={setFormOfUpload} formOfUpload={formOfUpload} handleReplaceImage={handleReplaceImage} isReplaceImage={isReplaceImage}/>\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t\t<Popover\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={currentContainerColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<style>\r\n    {`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n  </style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ImageSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACCC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,KAAK,QACC,eAAe;AACtB,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,SAASC,cAAc,QAAQ,eAAe;AAI9C,SACCC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,QAAQ,EACRC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,SAAS,QACH,6BAA6B;AACpC,OAAOC,cAAc,IACpBC,4BAA4B,EAC5BC,wBAAwB,EACxBC,wBAAwB,EACxBC,cAAc,EACdC,cAAc,QACR,4BAA4B;AACnC,SAASC,YAAY,QAAqB,aAAa;AACvD,OAAO,qBAAqB;AAC5B,OAAOC,0BAA0B,MAAM,yCAAyC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjF,MAAMC,YAAY,GAAGA,CAAC;EAAEC,WAAW;EAAEC,YAAY;EAAEC,QAAQ;EAAEC,OAAO;EAAEC;AAAqB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChG,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGhC,cAAc,CAAC,CAAC;EACzC,MAAM;IACLiC,WAAW;IACXC,eAAe;IACfC,aAAa;IACbC,gBAAgB;IAChBC,YAAY;IACZC,mBAAmB;IACnBC,oBAAoB;IACpBC,oBAAoB;IACpBC,SAAS;IACTjB,WAAW,EAAEkB;EACd,CAAC,GAAG/B,cAAc,CAAEgC,KAAK,IAAKA,KAAK,CAAC;EACpC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC8D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAA2C,MAAM,CAAC;EAE1G,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAS,CAAC,CAAC;EAEzD,MAAMkE,YAAY,GAAGA,CAAA,KAAM;IAC1BD,cAAc,CAACE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAChCR,eAAe,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC3BT,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD,MAAM,CAACU,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtE,QAAQ,CAAkD;IAC7GuE,kBAAkB,EAAE,EAAE;IACtBC,MAAM,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAAC2E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5E,QAAQ,CAAqB,IAAI,CAAC;EACxF,MAAM,CAAC6E,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG9E,QAAQ,CAInE;IAAEuE,kBAAkB,EAAE,EAAE;IAAEQ,OAAO,EAAE,KAAK;IAAEC,MAAM,EAAEtD;EAA6B,CAAC,CAAC;EAEpF,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlF,QAAQ,CAAC,MAAM,CAAC;EAC5D,MAAM,CAACmF,WAAW,EAAEC,YAAY,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqF,YAAY,EAAEC,eAAe,CAAC,GAAGtF,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACuF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxF,QAAQ,CAAqB,IAAI,CAAC;EAClF,MAAM,CAACyF,aAAa,EAAEC,gBAAgB,CAAC,GAAG1F,QAAQ,CAAS,SAAS,CAAC;EACrE,MAAM,CAAC2F,cAAc,EAAEC,eAAe,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM6F,aAAa,GAAG5F,MAAM,CAAqB,IAAI,CAAC;EACtD,MAAM,CAAC6F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/F,QAAQ,CAAC;IACxDgG,YAAY,EAAE,CAAC,CAAC;IAChBC,eAAe,EAAE,CAAC;EACnB,CAAC,CAAC;EAGF,MAAMC,mBAAmB,GAAGC,OAAO,CAACZ,gBAAgB,CAAC;EAErD,MAAMa,kBAAkB,GAAIC,KAAU,IAAK;IAC1CnB,iBAAiB,CAACmB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACtC,CAAC;EAED,MAAMC,mBAAmB,GAAIH,KAAoC,IAAK;IACrEb,mBAAmB,CAACa,KAAK,CAACI,aAAa,CAAC;EACzC,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACxClB,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EACD,MAAMmB,mBAAwC,GAAG;IAChDC,KAAK,EAAE,MAAM;IACb5B,MAAM,EAAE,MAAM;IACd6B,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACX,CAAC;EAED,MAAMC,UAA+B,GAAG;IACvCP,KAAK,EAAE,MAAM;IACb5B,MAAM,EAAE,MAAM;IACdiC,MAAM,EAAE,CAAC;IACTD,OAAO,EAAE,CAAC;IACVI,YAAY,EAAE;EACf,CAAC;EAED,MAAMC,YAAiC,GAAG;IACzCR,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBQ,GAAG,EAAE,MAAM;IACXC,SAAS,EAAE;EACZ,CAAC;EAED,MAAMC,aAAkC,GAAG;IAC1CX,OAAO,EAAE,MAAM;IACfY,aAAa,EAAE,QAAQ;IACvBV,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBF,KAAK,EAAE;EACR,CAAC;EAED,MAAMc,iBAAiB,GAAIrB,KAA0C,IAAK;IAAA,IAAAsB,mBAAA;IACzE,MAAMC,IAAI,IAAAD,mBAAA,GAAGtB,KAAK,CAACC,MAAM,CAACrF,KAAK,cAAA0G,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MAAA,IAAAC,oBAAA;MACT,MAAMC,KAAK,GAAGF,IAAI,CAACG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;MAC/B,MAAMC,SAAS,GAAGH,KAAK,CAACI,GAAG,CAAC,CAAC;MAG5B,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,IAAI,CAACF,SAAS,EAAG;QACvCpE,kBAAkB,CAAC,6DAA6D,CAAC;QAC5EE,mBAAmB,CAAC,OAAO,CAAC;QAClCJ,eAAe,CAAC,IAAI,CAAC;QACrB0C,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MAEF;MACH,IAAGqB,IAAI,CAACG,IAAI,CAACI,MAAM,GAAG,GAAG,EAAC;QAC1BtE,kBAAkB,CAAC,4CAA4C,CAAC;QAC1DE,mBAAmB,CAAC,OAAO,CAAC;QACjCJ,eAAe,CAAC,IAAI,CAAC;QACrB0C,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MACN;MACDhE,YAAY,EAAAsF,oBAAA,GAACxB,KAAK,CAACC,MAAM,CAACrF,KAAK,cAAA4G,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC,CAACE,IAAI,CAAC;MAE1C,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxB,MAAMC,WAAW,GAAGH,MAAM,CAACI,MAAgB;QAC3ChF,aAAa,CAAC+E,WAAW,CAAC;QAC1BjG,WAAW,CAACiG,WAAW,CAAC;QACxBxF,WAAW,CAACE,aAAa,CAACwF,WAAW,EAAE;UACtCC,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBY,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;UACvBC,GAAG,EAAEP,WAAW;UAChBQ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEnH;QACZ,CAAC,CAAC;MACH,CAAC;MACDuG,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC;IAC3B;IACAxC,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAM8D,wBAAwB,GAAItB,IAAgB,IAAK;IACtD,IAAIA,IAAI,EAAE;MACTpE,aAAa,CAACoE,IAAI,CAACuB,GAAG,CAAC;MACvB7G,WAAW,CAACsF,IAAI,CAACuB,GAAG,CAAC;MACrB,IAAIxD,cAAc,EAAE;QACnBxC,YAAY,CAACF,aAAa,CAACwF,WAAW,EAAExF,aAAa,CAACmG,QAAQ,EAAE;UAC/DV,OAAO,EAAEd,IAAI,CAACyB,QAAQ;UACtBV,EAAE,EAAE1F,aAAa,CAACmG,QAAQ;UAC1BN,GAAG,EAAElB,IAAI,CAACuB,GAAG;UACbJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEnH;QACZ,CAAC,CAAC;QACF+D,eAAe,CAAC,KAAK,CAAC;MACvB,CAAC,MAAM;QACN7C,WAAW,CAACE,aAAa,CAACwF,WAAW,EAAE;UACtCC,OAAO,EAAEd,IAAI,CAACyB,QAAQ;UACtBV,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;UAAE;UACzBC,GAAG,EAAElB,IAAI,CAACuB,GAAG;UAAE;UACfJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEnH;QACZ,CAAC,CAAC;MACH;IACD;IACAuD,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EACD,MAAMkE,kBAAkB,GAAIjD,KAA0C,IAAK;IAAA,IAAAkD,oBAAA;IAC1E,MAAM3B,IAAI,IAAA2B,oBAAA,GAAGlD,KAAK,CAACC,MAAM,CAACrF,KAAK,cAAAsI,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC;IACpC,IAAI3B,IAAI,EAAE;MACT,MAAMQ,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxBnF,YAAY,CAACF,aAAa,CAACwF,WAAW,EAAExF,aAAa,CAACmG,QAAQ,EAAE;UAC/DV,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBY,EAAE,EAAE1F,aAAa,CAACmG,QAAQ;UAC1BN,GAAG,EAAEV,MAAM,CAACI,MAAM;UAClBO,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEnH;QACZ,CAAC,CAAC;MACH,CAAC;MACDuG,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAM4B,WAAW,GAAGA,CACnBnD,KAAoC,EACpCoC,WAAmB,EACnBgB,OAAe,EACf1E,OAAgB,EAChB2E,aAAqB,KACjB;IAEJ,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACtD,KAAK,CAACC,MAAM,CAACqC,EAAE,CAAC,EAAE;IAC5DzF,gBAAgB,CAAC;MAChBkG,QAAQ,EAAEK,OAAO;MACjBhB,WAAW,EAAEA,WAAW;MAExBlC,KAAK,EAAEF,KAAK,CAACI;IACd,CAAC,CAAC;IACFjB,mBAAmB,CAAC,IAAI,CAAC;IACzBV,0BAA0B,CAAC;MAC1BP,kBAAkB,EAAEkE,WAAW;MAC/B1D,OAAO;MACPC,MAAM,EAAE0E;IACT,CAAC,CAAC;IACFpF,qBAAqB,CAAC;MACrBC,kBAAkB,EAAE,EAAE;MACtBC,MAAM,EAAE;IACT,CAAC,CAAC;EACH,CAAC;EAED,MAAMoF,WAAW,GAAGA,CAAA,KAAM;IACzB1G,gBAAgB,CAAC;MAChBkG,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,EAAE;MAEflC,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC;EAED,MAAMsD,IAAI,GAAG1D,OAAO,CAAClD,aAAa,CAACsD,KAAK,CAAC;EACzC,MAAMuD,eAAe,GAAG3D,OAAO,CAACxB,mBAAmB,CAAC;EAEpD,MAAMgE,EAAE,GAAGkB,IAAI,GAAG,eAAe,GAAGE,SAAS;EAE7C,MAAMC,oBAAoB,GAAIC,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAItI,wBAAwB,EAAE;IAC5C,MAAMuI,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,GAAGnI,cAAc,EAAEH,wBAAwB,CAAC;IACjF2B,oBAAoB,CAACL,aAAa,CAACwF,WAAW,EAAE,OAAO,EAAE;MACxDzD,MAAM,EAAEkF;IACT,CAAC,CAAC;IACFpF,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAEkF;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,MAAMG,oBAAoB,GAAIJ,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAIrI,wBAAwB,EAAE;IAC5C,MAAMsI,SAAS,GAAGC,IAAI,CAACG,GAAG,CAACL,UAAU,GAAGnI,cAAc,EAAEF,wBAAwB,CAAC;IACjF0B,oBAAoB,CAACL,aAAa,CAACwF,WAAW,EAAE,OAAO,EAAE;MACxDzD,MAAM,EAAEkF;IACT,CAAC,CAAC;IACFpF,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAEkF;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAChC,CAAAA,qBAAA,GAAAC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,cAAAF,qBAAA,uBAAzCA,qBAAA,CAA2CG,KAAK,CAAC,CAAC;EAEnD,CAAC;EAED,MAAMC,qBAAqB,GAC1B,EAAAhI,qBAAA,GAAAI,eAAe,CAAC6H,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACnC,EAAE,KAAK1F,aAAa,CAACwF,WAAW,CAAC,cAAA7F,qBAAA,uBAArEA,qBAAA,CAAuEmI,KAAK,CAAChC,eAAe,KAAI,aAAa;EAE9G,MAAMiC,mBAAmB,GAAGA,CAAA,KAAM;IACjC9H,gBAAgB,CAAC;MAChBkG,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,EAAE;MAEflC,KAAK,EAAE;IACR,CAAC,CAAC;IAGFlD,oBAAoB,CAACJ,aAAa,CAACwF,WAAW,CAAC;IAG/C,IAAIjG,QAAQ,EAAE;MACbA,QAAQ,CAAC,CAAC;IACX;EACD,CAAC;EAGD,MAAMyI,gBAAgB,GAAI5E,KAA4C,IAAK;IAC1E,IAAIA,KAAK,CAAC6E,GAAG,KAAK,OAAO,IAAIzG,SAAS,EAAE;MACvC1B,WAAW,CAACE,aAAa,CAACwF,WAAW,EAAE;QACtCC,OAAO,EAAE,WAAW;QACpBC,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;QACvBC,GAAG,EAAErE,SAAS;QACdsE,eAAe,EAAE,aAAa;QAC9BC,SAAS,EAAEnH;MACZ,CAAC,CAAC;MACFyC,qBAAqB,CAAC;QACrBC,kBAAkB,EAAE,EAAE;QACtBC,MAAM,EAAE;MACT,CAAC,CAAC;IACH;EACD,CAAC;EAED,MAAM2G,uBAAuB,GAAGA,CAAA,KAAM;IAErC,IAAIzI,eAAe,EAAE;MACpB;IACD;IAGAU,mBAAmB,CAACH,aAAa,CAACwF,WAAW,CAAC;IAG9C,IAAIhG,OAAO,EAAE;MACZA,OAAO,CAAC,CAAC;IACV;EACD,CAAC;EAED,MAAM2I,sBAAsB,GAAGA,CAAA,KAAM;IACpCxG,sBAAsB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMyG,iBAAiB,GAAIC,KAAkB,IAAK;IACjD5F,gBAAgB,CAAC4F,KAAK,CAACC,GAAG,CAAC;IAC3BjI,oBAAoB,CAACL,aAAa,CAACwF,WAAW,EAAE,OAAO,EAAE;MACxDM,eAAe,EAAEuC,KAAK,CAACC;IACxB,CAAC,CAAC;EACH,CAAC;EAED,MAAMC,0BAA0B,GAAInF,KAAoC,IAAK;IAC5EzB,sBAAsB,CAACyB,KAAK,CAACI,aAAa,CAAC;EAC5C,CAAC;EAGD,MAAMgF,qBAAqB,GAAGA,CAAA,KAAM;IAEnC,MAAMC,aAAa,GAAGjB,QAAQ,CAACkB,aAAa,CAAC,qCAAqC,CAAC;IACnF,IAAID,aAAa,EAAE;MAClB,MAAME,IAAI,GAAGF,aAAa,CAACG,qBAAqB,CAAC,CAAC;MAClD,OAAO;QACNC,GAAG,EAAEF,IAAI,CAACE,GAAG;QACbC,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfnF,KAAK,EAAEgF,IAAI,CAAChF,KAAK;QACjB5B,MAAM,EAAE4G,IAAI,CAAC5G;MACd,CAAC;IACF;IAEA,MAAMgH,iBAAiB,GAAGvB,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IAChE,IAAIsB,iBAAiB,EAAE;MACtB,MAAMJ,IAAI,GAAGI,iBAAiB,CAACH,qBAAqB,CAAC,CAAC;MACtD,OAAO;QACNC,GAAG,EAAEF,IAAI,CAACE,GAAG;QACbC,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfnF,KAAK,EAAEgF,IAAI,CAAChF,KAAK;QACjB5B,MAAM,EAAE4G,IAAI,CAAC5G;MACd,CAAC;IACF;IACA,OAAO,IAAI;EACZ,CAAC;EAGD,MAAMiH,uBAAuB,GAAGA,CAAA,KAAM;IACrC,MAAMC,aAAa,GAAGT,qBAAqB,CAAC,CAAC;IAC7C,IAAIS,aAAa,EAAE;MAClB,MAAMC,aAAa,GAAG,EAAE;MACxB,MAAMC,WAAW,GAAG,EAAE;MACtB,MAAMC,YAAY,GAAG,CAAC;MACtB,MAAMC,cAAc,GAAGC,MAAM,CAACC,WAAW;MAGzC,MAAMC,mBAAmB,GAAGP,aAAa;MAEzC,IAAIQ,WAAW;MAGf,MAAMC,eAAe,GAAGF,mBAAmB,CAACX,GAAG,GAAGK,aAAa,GAAGC,WAAW;MAG7E,IAAIO,eAAe,IAAIN,YAAY,EAAE;QAEpCK,WAAW,GAAGC,eAAe;MAC9B,CAAC,MAAM;QAEN,MAAMC,mBAAmB,GAAGH,mBAAmB,CAACX,GAAG,GAAGK,aAAa,GAAGE,YAAY;QAElF,IAAIO,mBAAmB,IAAI,EAAE,EAAE;UAE9BF,WAAW,GAAGC,eAAe;QAC9B,CAAC,MAAM,IAAIC,mBAAmB,IAAI,EAAE,EAAE;UAErCF,WAAW,GAAGD,mBAAmB,CAACX,GAAG,GAAGK,aAAa,GAAG,EAAE;QAC3D,CAAC,MAAM,IAAIS,mBAAmB,IAAI,CAAC,EAAE;UAEpCF,WAAW,GAAGD,mBAAmB,CAACX,GAAG,GAAGK,aAAa,GAAG,CAAC;QAC1D,CAAC,MAAM;UAENO,WAAW,GAAGvC,IAAI,CAACG,GAAG,CAAC+B,YAAY,EAAEI,mBAAmB,CAACX,GAAG,GAAGK,aAAa,GAAG,CAAC,CAAC;QAClF;MACD;MAGA,MAAMU,cAAc,GAAGP,cAAc,GAAGH,aAAa;MACrDO,WAAW,GAAGvC,IAAI,CAACC,GAAG,CAACsC,WAAW,EAAEG,cAAc,CAAC;MAGnD,IAAIC,YAAY,GAAGZ,aAAa,CAACH,IAAI,GAAIG,aAAa,CAACtF,KAAK,GAAG,CAAE,GAAG,GAAG;MAGvE,IAAIkG,YAAY,GAAG,EAAE,EAAE;QACtBA,YAAY,GAAG,EAAE;MAClB;MAEA,MAAMC,YAAY,GAAG,GAAG;MACxB,MAAMC,aAAa,GAAGT,MAAM,CAACU,UAAU;MACvC,IAAIH,YAAY,GAAGC,YAAY,GAAGC,aAAa,GAAG,EAAE,EAAE;QACrDF,YAAY,GAAGE,aAAa,GAAGD,YAAY,GAAG,EAAE;MACjD;MAEA,OAAO;QACNjB,GAAG,EAAEY,WAAW;QAChBX,IAAI,EAAEe,YAAY;QAClBI,QAAQ,EAAE,OAAgB;QAC1BC,MAAM,EAAE;MACT,CAAC;IACF;IACA,OAAO,CAAC,CAAC;EACV,CAAC;EAGD,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACxC,MAAMlB,aAAa,GAAGT,qBAAqB,CAAC,CAAC;IAC7C,IAAIS,aAAa,EAAE;MAClB,MAAMI,cAAc,GAAGC,MAAM,CAACC,WAAW;MACzC,MAAMQ,aAAa,GAAGT,MAAM,CAACU,UAAU;MACvC,MAAMI,mBAAmB,GAAG,GAAG;MAC/B,MAAMC,kBAAkB,GAAG,GAAG;MAC9B,MAAMb,mBAAmB,GAAGP,aAAa;MAGzC,IAAIY,YAAY,GAAGL,mBAAmB,CAACV,IAAI,GAAGU,mBAAmB,CAAC7F,KAAK,GAAG,EAAE;MAC5E,IAAI8F,WAAW,GAAGD,mBAAmB,CAACX,GAAG,GAAIW,mBAAmB,CAACzH,MAAM,GAAG,CAAE,GAAIqI,mBAAmB,GAAC,CAAE,GAAG,EAAE;MAG3G,IAAIP,YAAY,GAAGQ,kBAAkB,GAAGN,aAAa,GAAG,EAAE,EAAE;QAC3DF,YAAY,GAAGL,mBAAmB,CAACV,IAAI,GAAGuB,kBAAkB,GAAG,EAAE;MAClE;MAGA,IAAIZ,WAAW,GAAG,EAAE,EAAE;QACrBA,WAAW,GAAG,EAAE;MACjB,CAAC,MAAM,IAAIA,WAAW,GAAGW,mBAAmB,GAAGf,cAAc,GAAG,EAAE,EAAE;QACnEI,WAAW,GAAGJ,cAAc,GAAGe,mBAAmB,GAAG,EAAE;MACxD;MAGAP,YAAY,GAAG3C,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACC,GAAG,CAAC0C,YAAY,EAAEE,aAAa,GAAGM,kBAAkB,GAAG,EAAE,CAAC,CAAC;MAE5F,OAAO;QACNxB,GAAG,EAAEY,WAAW;QAChBX,IAAI,EAAEe,YAAY;QAClBI,QAAQ,EAAE,OAAgB;QAC1BC,MAAM,EAAE;MACT,CAAC;IACF;IACA,OAAO,CAAC,CAAC;EACV,CAAC;EAGD,MAAMI,sBAAsB,GAAGA,CAAA,KAAM;IACpCxH,mBAAmB,CAAC;MACnBC,YAAY,EAAEiG,uBAAuB,CAAC,CAAC;MACvChG,eAAe,EAAEmH,0BAA0B,CAAC;IAC7C,CAAC,CAAC;EACH,CAAC;EAGDrN,SAAS,CAAC,MAAM;IACf,MAAMyN,YAAY,GAAGA,CAAA,KAAM;MAC1BD,sBAAsB,CAAC,CAAC;IACzB,CAAC;IAED,MAAME,YAAY,GAAGA,CAAA,KAAM;MAC1BF,sBAAsB,CAAC,CAAC;IACzB,CAAC;IAGD,IAAI1D,IAAI,IAAI3D,mBAAmB,EAAE;MAChCqH,sBAAsB,CAAC,CAAC;MAGxB,MAAMG,qBAAqB,GAAGC,WAAW,CAAC,MAAM;QAC/C,IAAI9D,IAAI,IAAI3D,mBAAmB,EAAE;UAChCqH,sBAAsB,CAAC,CAAC;QACzB,CAAC,MAAM;UACNK,aAAa,CAACF,qBAAqB,CAAC;QACrC;MACD,CAAC,EAAE,GAAG,CAAC;MAENnB,MAAM,CAASsB,0BAA0B,GAAGH,qBAAqB;IACnE;IAGAnB,MAAM,CAACuB,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IAC/CjB,MAAM,CAACuB,gBAAgB,CAAC,QAAQ,EAAEL,YAAY,CAAC;IAG/C,MAAMM,uBAAuB,GAAGtD,QAAQ,CAACkB,aAAa,CAAC,wBAAwB,CAAC;IAChF,IAAIoC,uBAAuB,EAAE;MAC5BA,uBAAuB,CAACD,gBAAgB,CAAC,aAAa,EAAEL,YAAY,CAAC;MACrEM,uBAAuB,CAACD,gBAAgB,CAAC,QAAQ,EAAEL,YAAY,CAAC;IACjE;IAGA,MAAMzB,iBAAiB,GAAGvB,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IAChE,MAAMgB,aAAa,GAAGjB,QAAQ,CAACkB,aAAa,CAAC,qCAAqC,CAAC;IACnF,MAAMqC,gBAAgB,GAAGvD,QAAQ,CAACkB,aAAa,CAAC,wBAAwB,CAAC;IAEzE,IAAIsC,cAAqC,GAAG,IAAI;IAChD,IAAIC,gBAAyC,GAAG,IAAI;IAEpD,IAAI3B,MAAM,CAAC4B,cAAc,EAAE;MAC1BF,cAAc,GAAG,IAAIE,cAAc,CAAEC,OAAO,IAAK;QAChD,IAAIC,oBAAoB,GAAG,KAAK;QAChCD,OAAO,CAACE,OAAO,CAAEC,KAAK,IAAK;UAC1B,MAAM;YAAE3H,KAAK;YAAE5B;UAAO,CAAC,GAAGuJ,KAAK,CAACC,WAAW;UAC3C,MAAMC,OAAO,GAAGF,KAAK,CAACjI,MAAqB;UAC3C,MAAMoI,SAAS,GAAGC,UAAU,CAACF,OAAO,CAACG,OAAO,CAACF,SAAS,IAAI,GAAG,CAAC;UAC9D,MAAMG,UAAU,GAAGF,UAAU,CAACF,OAAO,CAACG,OAAO,CAACC,UAAU,IAAI,GAAG,CAAC;UAEhE,IAAI1E,IAAI,CAAC2E,GAAG,CAAClI,KAAK,GAAG8H,SAAS,CAAC,GAAG,CAAC,IAAIvE,IAAI,CAAC2E,GAAG,CAAC9J,MAAM,GAAG6J,UAAU,CAAC,GAAG,CAAC,EAAE;YACzER,oBAAoB,GAAG,IAAI;YAC3BI,OAAO,CAACG,OAAO,CAACF,SAAS,GAAG9H,KAAK,CAACmI,QAAQ,CAAC,CAAC;YAC5CN,OAAO,CAACG,OAAO,CAACC,UAAU,GAAG7J,MAAM,CAAC+J,QAAQ,CAAC,CAAC;UAC/C;QACD,CAAC,CAAC;QAEF,IAAIV,oBAAoB,EAAE;UACzBd,sBAAsB,CAAC,CAAC;UACxByB,UAAU,CAAC,MAAM;YAChBzB,sBAAsB,CAAC,CAAC;UACzB,CAAC,EAAE,EAAE,CAAC;QACP;MACD,CAAC,CAAC;MAGF,IAAIvB,iBAAiB,EAAE;QACtBiC,cAAc,CAACgB,OAAO,CAACjD,iBAAiB,CAAC;MAC1C;MACA,IAAIN,aAAa,EAAE;QAClBuC,cAAc,CAACgB,OAAO,CAACvD,aAAa,CAAC;MACtC;MACA,IAAIsC,gBAAgB,EAAE;QACrBC,cAAc,CAACgB,OAAO,CAACjB,gBAAgB,CAAC;MACzC;IACD;IAGA,IAAIhC,iBAAiB,IAAIO,MAAM,CAAC2C,gBAAgB,EAAE;MACjDhB,gBAAgB,GAAG,IAAIgB,gBAAgB,CAAEC,SAAS,IAAK;QACtD,IAAIC,YAAY,GAAG,KAAK;QACxBD,SAAS,CAACb,OAAO,CAAEe,QAAQ,IAAK;UAC/B,IAAIA,QAAQ,CAACC,IAAI,KAAK,WAAW,IAC/BD,QAAQ,CAACC,IAAI,KAAK,YAAY,IAC9B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC3F,QAAQ,CAAC0F,QAAQ,CAACE,aAAa,IAAI,EAAE,CAAE,EAAE;YAC7DH,YAAY,GAAG,IAAI;UACpB;QACD,CAAC,CAAC;QAEF,IAAIA,YAAY,EAAE;UACjBJ,UAAU,CAAC,MAAM;YAChBzB,sBAAsB,CAAC,CAAC;UACzB,CAAC,EAAE,EAAE,CAAC;QACP;MACD,CAAC,CAAC;MAEFW,gBAAgB,CAACe,OAAO,CAACjD,iBAAiB,EAAE;QAC3CwD,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;MACnC,CAAC,CAAC;IACH;IAEA,OAAO,MAAM;MACZpD,MAAM,CAACqD,mBAAmB,CAAC,QAAQ,EAAEpC,YAAY,CAAC;MAClDjB,MAAM,CAACqD,mBAAmB,CAAC,QAAQ,EAAEnC,YAAY,CAAC;MAGlD,MAAMM,uBAAuB,GAAGtD,QAAQ,CAACkB,aAAa,CAAC,wBAAwB,CAAC;MAChF,IAAIoC,uBAAuB,EAAE;QAC5BA,uBAAuB,CAAC6B,mBAAmB,CAAC,aAAa,EAAEnC,YAAY,CAAC;QACxEM,uBAAuB,CAAC6B,mBAAmB,CAAC,QAAQ,EAAEnC,YAAY,CAAC;MACpE;MAGA,IAAKlB,MAAM,CAASsB,0BAA0B,EAAE;QAC/CD,aAAa,CAAErB,MAAM,CAASsB,0BAA0B,CAAC;QACzD,OAAQtB,MAAM,CAASsB,0BAA0B;MAClD;MAEA,IAAII,cAAc,EAAE;QACnBA,cAAc,CAAC4B,UAAU,CAAC,CAAC;MAC5B;MACA,IAAI3B,gBAAgB,EAAE;QACrBA,gBAAgB,CAAC2B,UAAU,CAAC,CAAC;MAC9B;IACD,CAAC;EACF,CAAC,EAAE,CAAChG,IAAI,EAAE3D,mBAAmB,CAAC,CAAC;EAE/B,oBACChE,OAAA,CAAAE,SAAA;IAAA0N,QAAA,GACE9M,eAAe,CAAC+M,GAAG,CAAEjF,IAAI,IAAK;MAAA,IAAAkF,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,WAAA;MAC9B,MAAMC,QAAQ,IAAAJ,aAAA,GAAGlF,IAAI,CAACuF,MAAM,CAAC,CAAC,CAAC,cAAAL,aAAA,uBAAdA,aAAA,CAAgBlH,GAAG;MACpC,MAAMW,OAAO,IAAAwG,cAAA,GAAGnF,IAAI,CAACuF,MAAM,CAAC,CAAC,CAAC,cAAAJ,cAAA,uBAAdA,cAAA,CAAgBtH,EAAE;MAClC,MAAMK,SAAS,GAAG,EAAAkH,cAAA,GAAApF,IAAI,CAACuF,MAAM,CAAC,CAAC,CAAC,cAAAH,cAAA,uBAAdA,cAAA,CAAgBlH,SAAS,KAAInH,cAAc;MAC7D,MAAMyO,gBAAgB,GAAG,CAACxF,IAAI,aAAJA,IAAI,wBAAAqF,WAAA,GAAJrF,IAAI,CAAEC,KAAK,cAAAoF,WAAA,uBAAXA,WAAA,CAAanL,MAAM,KAAetD,4BAA4B;MACxF,MAAMiH,EAAE,GAAGmC,IAAI,CAACnC,EAAE;MAClB,oBACCzG,OAAA,CAAChC,GAAG;QAEHqQ,EAAE,EAAE;UACH3J,KAAK,EAAE,MAAM;UACb5B,MAAM,EAAE,MAAM;UACd6B,OAAO,EAAE,MAAM;UACfY,aAAa,EAAE,QAAQ;UACvBX,cAAc,EAAE,YAAY;UAC5BC,UAAU,EAAE,QAAQ;UAEpBE,MAAM,EAAE,KAAK;UACbC,QAAQ,EAAE;QACX,CAAE;QAAA4I,QAAA,eAEF5N,OAAA,CAAChC,GAAG;UACHqQ,EAAE,EAAE;YACH,GAAG5J,mBAAmB;YACtBoC,eAAe,EAAE+B,IAAI,CAACC,KAAK,CAAChC,eAAe;YAC3C/D,MAAM,EAAE,GAAG8F,IAAI,CAACC,KAAK,CAAC/F,MAAM;UAC7B,CAAE;UACFwL,OAAO,EAAGC,CAAC,IAAKjH,WAAW,CAACiH,CAAC,EAAE9H,EAAE,EAAEc,OAAO,EAAE2G,QAAQ,GAAG,IAAI,GAAG,KAAK,EAAEE,gBAAgB,CAAE;UACvFI,SAAS,EAAE,KAAM;UACjB/H,EAAE,EAAEA,EAAG;UACPgI,WAAW,EAAEA,CAAA,KAAM;YAClBzN,gBAAgB,CAAC;cAChBkG,QAAQ,EAAEK,OAAO;cACjBhB,WAAW,EAAEE,EAAE;cACfpC,KAAK,EAAE;YACR,CAAC,CAAC;UACH,CAAE;UAAAuJ,QAAA,EAEDM,QAAQ,gBACRlO,OAAA;YACC0O,GAAG,EAAER,QAAS;YACdS,GAAG,EAAC,UAAU;YACd9F,KAAK,EAAE;cAAE,GAAG5D,UAAU;cAAE6B;YAAU;UAAE;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,gBAEF/O,OAAA,CAAChC,GAAG;YACHqQ,EAAE,EAAE;cACHW,SAAS,EAAE,QAAQ;cACnBtK,KAAK,EAAE,MAAM;cACb5B,MAAM,EAAE,MAAM;cACd6B,OAAO,EAAE,MAAM;cACfY,aAAa,EAAE,QAAQ;cACvBX,cAAc,EAAE;YACjB,CAAE;YAAAgJ,QAAA,gBAEF5N,OAAA,CAAChC,GAAG;cACHqQ,EAAE,EAAE/I,aAAc;cAClBkJ,SAAS,EAAE,KAAM;cAAAZ,QAAA,gBAEjB5N,OAAA;gBACCiP,uBAAuB,EAAE;kBAAEC,MAAM,EAAErQ;gBAAW,CAAE;gBAChDgK,KAAK,EAAE;kBAAElE,OAAO,EAAE;gBAAe;cAAE;gBAAAiK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACF/O,OAAA,CAAC/B,UAAU;gBACVkR,OAAO,EAAC,IAAI;gBACZC,KAAK,EAAC,QAAQ;gBACdf,EAAE,EAAE;kBAAEgB,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAM,CAAE;gBAAA1B,QAAA,EAE1ChN,SAAS,CAAC,aAAa;cAAC;gBAAAgO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEN/O,OAAA,CAAC/B,UAAU;cACVkR,OAAO,EAAC,OAAO;cACfC,KAAK,EAAC,QAAQ;cACdhG,KAAK,EAAC,eAAe;cACrBiF,EAAE,EAAE;gBAAEgB,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,EAEvBhN,SAAS,CAAC,4BAA4B;YAAC;cAAAgO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACb/O,OAAA,CAAC/B,UAAU;cACVkR,OAAO,EAAC,OAAO;cACfC,KAAK,EAAC,QAAQ;cACdhG,KAAK,EAAC,eAAe;cACrBiF,EAAE,EAAE;gBAAEhJ,SAAS,EAAE,KAAK;gBAAEgK,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,EAEzChN,SAAS,CAAC,IAAI;YAAC;cAAAgO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACZ5M,kBAAkB,CAACG,MAAM,IAAIH,kBAAkB,CAACE,kBAAkB,KAAKoE,EAAE,gBACzEzG,OAAA,CAAC5B,SAAS;cACTiG,KAAK,EAAE9B,SAAU;cACjBgN,QAAQ,EAAGhB,CAAC,IAAK/L,YAAY,CAAC+L,CAAC,CAACnK,MAAM,CAACC,KAAK,CAAE;cAC9CmL,SAAS,EAAEzG,gBAAiB;cAC5B0G,SAAS;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,gBAEF/O,OAAA,CAAChC,GAAG;cAACqQ,EAAE,EAAElJ,YAAa;cAAAyI,QAAA,gBACnB5N,OAAA,CAACzB,OAAO;gBAACmR,KAAK,EAAE9O,SAAS,CAAC,aAAa,CAAE;gBAAAgN,QAAA,eACpD5N,OAAA;kBAAK6I,KAAK,EAAE;oBAAE8G,aAAa,EAAE,MAAM;oBAAEC,MAAM,EAAC;kBAAS,CAAE;kBAAAhC,QAAA,eACrD5N,OAAA;oBACEiP,uBAAuB,EAAE;sBAAEC,MAAM,EAAEpQ;oBAAU,CAAE;oBAC/C+J,KAAK,EAAE;sBACLO,KAAK,EAAE,OAAO;sBACdwG,MAAM,EAAE,SAAS;sBACjBP,QAAQ,EAAE,MAAM;sBAChBQ,OAAO,EAAE,KAAK;sBACdF,aAAa,EAAE;oBACjB,CAAE;oBACFlJ,EAAE,EAAC,WAAW;oBACPqJ,SAAS,EAAC;kBAAoB;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGG/O,OAAA,CAACzB,OAAO;gBAACmR,KAAK,EAAE9O,SAAS,CAAC,aAAa,CAAE;gBAAAgN,QAAA,eACxC5N,OAAA;kBACCsO,OAAO,EAAGnK,KAAK,IAAK,CAEpB,CAAE;kBACL8K,uBAAuB,EAAE;oBAAEC,MAAM,EAAEnQ;kBAAM,CAAE;kBAC3C8J,KAAK,EAAE;oBAAEO,KAAK,EAAE,OAAO;oBAAEwG,MAAM,EAAE,SAAS;oBAAEP,QAAQ,EAAE,MAAM;oBAAEQ,OAAO,EAAE;kBAAM,CAAE;kBAC/EpJ,EAAE,EAAC,QAAQ;kBACXqJ,SAAS,EAAC;gBAAoB;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACV/O,OAAA,CAACzB,OAAO;gBAACmR,KAAK,EAAE9O,SAAS,CAAC,aAAa,CAAE;gBAAAgN,QAAA,eAC3C5N,OAAA;kBACIsO,OAAO,EAAGnK,KAAK,IAAK;oBAAA,IAAA4L,sBAAA;oBAEtB5L,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6L,eAAe,CAAC,CAAC;oBACxB,CAAAD,sBAAA,GAAAxH,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,cAAAuH,sBAAA,uBAAtCA,sBAAA,CAAwCtH,KAAK,CAAC,CAAC;kBAChD,CAAE;kBACFhC,EAAE,EAAC,cAAc;kBACjBqJ,SAAS,EAAC,oBAAoB;kBAC9Bb,uBAAuB,EAAE;oBAAEC,MAAM,EAAElQ;kBAAW,CAAE;kBAChD6J,KAAK,EAAE;oBAAEO,KAAK,EAAE,OAAO;oBAAEwG,MAAM,EAAE,SAAS;oBAAEP,QAAQ,EAAE;kBAAO;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACb/O,OAAA;gBACCoN,IAAI,EAAC,MAAM;gBACX3G,EAAE,EAAC,aAAa;gBAChBoC,KAAK,EAAE;kBAAElE,OAAO,EAAE;gBAAO,CAAE;gBAC3BsL,MAAM,EAAC,SAAS;gBAChBV,QAAQ,EAAE/J;cAAkB;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACF/O,OAAA,CAACxB,QAAQ;gBAACmJ,IAAI,EAAEnG,YAAa;gBAAC0O,gBAAgB,EAAE,IAAK;gBAACC,OAAO,EAAEjO,aAAc;gBAACkO,YAAY,EAAE;kBAAEC,QAAQ,EAAE,QAAQ;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAA1C,QAAA,eACxI5N,OAAA,CAACvB,KAAK;kBAAC0R,OAAO,EAAEjO,aAAc;kBAACqO,QAAQ,EAAE3O,gBAAiB;kBAACyM,EAAE,EAAE;oBAAE3J,KAAK,EAAE;kBAAO,CAAE;kBAAAkJ,QAAA,EAC/ElM;gBAAe;kBAAAkN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC,GApJDtI,EAAE;QAAAmI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqJH,CAAC;IAER,CAAC,CAAC,EACD9K,OAAO,CAAClD,aAAa,CAACsD,KAAK,CAAC,gBAC5BrE,OAAA,CAAC9B,OAAO;MACP4R,SAAS,EAAC,sBAAsB;MAChCrJ,EAAE,EAAEA,EAAG;MACPkB,IAAI,EAAEA,IAAK;MACX6I,QAAQ,EAAE,IAAK;MACfL,OAAO,EAAEzI,WAAY;MACrB+I,eAAe,EAAC,MAAM;MACtBC,SAAS,EAAE;QACVC,KAAK,EAAE;UACN9H,KAAK,EAAE;YACN,GAAGjF,gBAAgB,CAACE,YAAY;YAChChB,MAAM,EAAE,MAAM;YACd4B,KAAK,EAAE,MAAM;YACbI,OAAO,EAAE,UAAU;YACnB8L,UAAU,EAAE,MAAM;YAClBC,WAAW,EAAE;UACd;QACD;MACD,CAAE;MAAAjD,QAAA,eAEF5N,OAAA,CAAChC,GAAG;QACHqQ,EAAE,EAAE;UACH1J,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBO,GAAG,EAAE,MAAM;UACXtC,MAAM,EAAE,MAAM;UACdgC,OAAO,EAAE,QAAQ;UACjBuK,QAAQ,EAAE;QACX,CAAE;QAAAzB,QAAA,gBAEF5N,OAAA,CAAChC,GAAG;UAACqQ,EAAE,EAAE;YAAE1J,OAAO,EAAE;UAAO,CAAE;UAAAiJ,QAAA,EAC3BjL,uBAAuB,CAACN,kBAAkB,KAAKtB,aAAa,CAACwF,WAAW,IACzE5D,uBAAuB,CAACE,OAAO,gBAC9B7C,OAAA,CAAAE,SAAA;YAAA0N,QAAA,gBACC5N,OAAA;cAAMiP,uBAAuB,EAAE;gBAAEC,MAAM,EAAEjQ;cAAiB;YAAE;cAAA2P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/D/O,OAAA,CAAC/B,UAAU;cACVoR,QAAQ,EAAC,MAAM;cACfuB,UAAU,EAAE,KAAM;cAClBtC,OAAO,EAAEjG,kBAAmB;cAAAuF,QAAA,EAE1BhN,SAAS,CAAC,eAAe;YAAC;cAAAgO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACb/O,OAAA;cACCoN,IAAI,EAAC,MAAM;cACX3G,EAAE,EAAC,gBAAgB;cACnBoC,KAAK,EAAE;gBAAElE,OAAO,EAAE;cAAO,CAAE;cAC3BsL,MAAM,EAAC,SAAS;cAChBV,QAAQ,EAAEnI;YAAmB;cAAAwH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA,eACD,CAAC,GACA;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEZ/O,OAAA,CAAChC,GAAG;UACG8R,SAAS,EAAC,kBAAkB;UAC5BzB,EAAE,EAAE;YAAE1J,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAA+I,QAAA,gBAE9C5N,OAAA;YAAMiP,uBAAuB,EAAE;cAAEC,MAAM,EAAE9P;YAAc,CAAE;YACzDyJ,KAAK,EAAE;cAAElE,OAAO,EAAE;YAAO;UAAE;YAAAiK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC7B/O,OAAA,CAACzB,OAAO;YAACmR,KAAK,EAAE/M,uBAAuB,CAACG,MAAM,IAAIpD,wBAAwB,GAAGkB,SAAS,CAAC,wBAAwB,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAE;YAAAgN,QAAA,eAC/I5N,OAAA;cAAA4N,QAAA,eACC5N,OAAA,CAAC7B,UAAU;gBACVmQ,OAAO,EAAEA,CAAA,KAAMnG,oBAAoB,CAACxF,uBAAuB,CAACG,MAAM,CAAE;gBACpEgO,IAAI,EAAC,OAAO;gBACZC,QAAQ,EAAEpO,uBAAuB,CAACG,MAAM,IAAIpD,wBAAyB;gBACrE2O,EAAE,EAAE;kBACHwB,OAAO,EAAElN,uBAAuB,CAACG,MAAM,IAAIpD,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7EkQ,MAAM,EAAEjN,uBAAuB,CAACG,MAAM,IAAIpD,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAAkO,QAAA,eAEF5N,OAAA,CAACtB,UAAU;kBAAC2Q,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACV/O,OAAA,CAAC/B,UAAU;YAACoR,QAAQ,EAAC,MAAM;YAAAzB,QAAA,EAAEjL,uBAAuB,CAACG;UAAM;YAAA8L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzE/O,OAAA,CAACzB,OAAO;YAACmR,KAAK,EAAE/M,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAGmB,SAAS,CAAC,wBAAwB,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAE;YAAAgN,QAAA,eAC/I5N,OAAA;cAAA4N,QAAA,eACC5N,OAAA,CAAC7B,UAAU;gBACVmQ,OAAO,EAAEA,CAAA,KAAMxG,oBAAoB,CAACnF,uBAAuB,CAACG,MAAM,CAAE;gBACpEgO,IAAI,EAAC,OAAO;gBACZC,QAAQ,EAAEpO,uBAAuB,CAACG,MAAM,IAAIrD,wBAAyB;gBACrE4O,EAAE,EAAE;kBACHwB,OAAO,EAAElN,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7EmQ,MAAM,EAAEjN,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAAmO,QAAA,eAEF5N,OAAA,CAACrB,OAAO;kBAAC0Q,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN/O,OAAA,CAACzB,OAAO;UAACmR,KAAK,EAAE9O,SAAS,CAAC,UAAU,CAAE;UAAAgN,QAAA,eACtC5N,OAAA,CAAChC,GAAG;YAAC8R,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,gBAChC5N,OAAA,CAAChC,GAAG;cAAC8R,SAAS,EAAC,kBAAkB;cAAAlC,QAAA,eAChC5N,OAAA,CAAC7B,UAAU;gBACV2S,IAAI,EAAC,OAAO;gBACZxC,OAAO,EAAEhK,mBAAoB;gBAAAsJ,QAAA,eAE7B5N,OAAA;kBACCiP,uBAAuB,EAAE;oBAAEC,MAAM,EAAE7P;kBAAS,CAAE;kBAC9CwJ,KAAK,EAAE;oBAAEO,KAAK,EAAE;kBAAO;gBAAE;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEL/O,OAAA,CAAC9B,OAAO;cACE4R,SAAS,EAAC,cAAc;cAClCnI,IAAI,EAAE3D,mBAAoB;cAC1BwM,QAAQ,EAAE,IAAK;cACfL,OAAO,EAAE3L,0BAA2B;cACpCiM,eAAe,EAAC,MAAM;cACtBC,SAAS,EAAE;gBACVC,KAAK,EAAE;kBACN9H,KAAK,EAAE;oBACN,GAAGjF,gBAAgB,CAACG,eAAe;oBACnCW,KAAK,EAAE;kBACR;gBACD;cACD,CAAE;cAAAkJ,QAAA,eAEF5N,OAAA,CAAChC,GAAG;gBAACgT,CAAC,EAAE,CAAE;gBAAApD,QAAA,gBACT5N,OAAA,CAAChC,GAAG;kBACH2G,OAAO,EAAC,MAAM;kBACdC,cAAc,EAAC,eAAe;kBAC9BC,UAAU,EAAC,QAAQ;kBAAA+I,QAAA,gBAEnB5N,OAAA,CAAC/B,UAAU;oBACVkR,OAAO,EAAC,WAAW;oBACnBd,EAAE,EAAE;sBAAEjF,KAAK,EAAE;oBAAwB,CAAE;oBAAAwE,QAAA,EAErChN,SAAS,CAAC,kBAAkB;kBAAC;oBAAAgO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACb/O,OAAA,CAAC7B,UAAU;oBACV2S,IAAI,EAAC,OAAO;oBACZxC,OAAO,EAAE9J,0BAA2B;oBAAAoJ,QAAA,eAEpC5N,OAAA;sBACCiP,uBAAuB,EAAE;wBAAEC,MAAM,EAAE5P;sBAAU,CAAE;sBAC/CuJ,KAAK,EAAE;wBAAEO,KAAK,EAAE;sBAAQ;oBAAE;sBAAAwF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACN/O,OAAA,CAACzB,OAAO;kBAACmR,KAAK,EAAE9O,SAAS,CAAC,aAAa,CAAE;kBAAAgN,QAAA,eAC1C5N,OAAA,CAAChC,GAAG;oBAACiT,EAAE,EAAE,CAAE;oBAAArD,QAAA,gBACV5N,OAAA,CAAC/B,UAAU;sBACVkR,OAAO,EAAC,OAAO;sBACb/F,KAAK,EAAC,eAAe;sBACrBiF,EAAE,EAAE;wBAAE6C,YAAY,EAAE;sBAAO,CAAE;sBAAAtD,QAAA,EAE5BhN,SAAS,CAAC,eAAe;oBAAC;sBAAAgO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACb/O,OAAA,CAAC5B,SAAS;sBACT+S,MAAM;sBACNC,SAAS;sBACTjC,OAAO,EAAC,UAAU;sBAClB2B,IAAI,EAAC,OAAO;sBACZzM,KAAK,EAAEtB,cAAe;sBACtBwM,QAAQ,EAAErL,kBAAmB;sBAC7BmK,EAAE,EAAE;wBACH,0BAA0B,EAAE;0BAC3BgD,WAAW,EAAE;wBACd;sBACD,CAAE;sBACFN,QAAQ;sBAAAnD,QAAA,gBAEN5N,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,MAAM;wBAAAuJ,QAAA,EAAEhN,SAAS,CAAC,MAAM;sBAAC;wBAAAgO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACrD/O,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,cAAc;wBAAAuJ,QAAA,EAAEhN,SAAS,CAAC,eAAe;sBAAC;wBAAAgO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtE/O,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,SAAS;wBAAAuJ,QAAA,EAAEhN,SAAS,CAAC,UAAU;sBAAC;wBAAAgO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAC5D/O,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,cAAc;wBAAAuJ,QAAA,EAAEhN,SAAS,CAAC,eAAe;sBAAC;wBAAAgO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtE/O,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,WAAW;wBAAAuJ,QAAA,EAAEhN,SAAS,CAAC,YAAY;sBAAC;wBAAAgO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAChE/O,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,kBAAkB;wBAAAuJ,QAAA,EAAEhN,SAAS,CAAC,oBAAoB;sBAAC;wBAAAgO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACV/O,OAAA,CAAChC,GAAG;kBAACiT,EAAE,EAAE,CAAE;kBAAArD,QAAA,gBACV5N,OAAA,CAAC/B,UAAU;oBACVkR,OAAO,EAAC,OAAO;oBACf/F,KAAK,EAAC,eAAe;oBAAAwE,QAAA,EAEnBhN,SAAS,CAAC,kBAAkB;kBAAC;oBAAAgO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACb/O,OAAA,CAAChC,GAAG;oBACH2G,OAAO,EAAC,MAAM;oBACdS,GAAG,EAAE,CAAE;oBACP6L,EAAE,EAAE,CAAE;oBAAArD,QAAA,EAEL,CAAC,MAAM,EAAE,KAAK,CAAC,CAACC,GAAG,CAAEjF,IAAI,IAAK;sBAE9B,MAAM0I,gBAAgB,GAAGxQ,eAAe,CAAC6H,IAAI,CAAE4I,CAAC,IAAKA,CAAC,CAAC9K,EAAE,KAAK1F,aAAa,CAACwF,WAAW,CAAC;sBACxF,MAAMiL,YAAY,GAAGF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEnD,MAAM,CAACxF,IAAI,CAAE8I,GAAG,IAAKA,GAAG,CAAChL,EAAE,KAAK1F,aAAa,CAACmG,QAAQ,CAAC;sBAC9F,MAAMwK,gBAAgB,GAAG,CAAAF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1K,SAAS,KAAInH,cAAc;sBAGlE,MAAMgS,UAAU,GAAI/I,IAAI,KAAK,MAAM,IAAI8I,gBAAgB,KAAK,OAAO,IAC5D9I,IAAI,KAAK,KAAK,IAAI8I,gBAAgB,KAAK,SAAU;sBAExD,oBACC1R,OAAA,CAAC1B,MAAM;wBAENgQ,OAAO,EAAEA,CAAA,KACRjN,SAAS,CAACN,aAAa,CAACwF,WAAW,EAAExF,aAAa,CAACmG,QAAQ,EAAE0B,IAAsB,CACnF;wBACDuG,OAAO,EAAC,UAAU;wBAClB2B,IAAI,EAAC,OAAO;wBACZzC,EAAE,EAAE;0BACH3J,KAAK,EAAE,QAAQ;0BACf5B,MAAM,EAAE,MAAM;0BACdgC,OAAO,EAAE,WAAW;0BACpBM,GAAG,EAAE,MAAM;0BACXF,YAAY,EAAE,iBAAiB;0BAC/B0M,MAAM,EACLD,UAAU,GACP,iCAAiC,GACjC,kCAAkC;0BACtC9K,eAAe,EACd8K,UAAU,GAAG,yBAAyB,GAAG,0BAA0B;0BACpEE,mBAAmB,EAAE,UAAU;0BAC/BzI,KAAK,EAAE,OAAO;0BACd,SAAS,EAAE;4BACVvC,eAAe,EACd8K,UAAU,GAAG,yBAAyB,GAAG;0BAC3C;wBACD,CAAE;wBAAA/D,QAAA,EAEDhN,SAAS,CAACgI,IAAI;sBAAC,GA1BXA,IAAI;wBAAAgG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2BF,CAAC;oBAEX,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACV/O,OAAA,CAACzB,OAAO;UAACmR,KAAK,EAAE9O,SAAS,CAAC,kBAAkB,CAAE;UAAAgN,QAAA,eAC9C5N,OAAA,CAAChC,GAAG;YAAC8R,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChC5N,OAAA,CAAC7B,UAAU;cACVmQ,OAAO,EAAEhF,0BAA2B;cACpCwH,IAAI,EAAC,OAAO;cAAAlD,QAAA,eAEZ5N,OAAA;gBACA6I,KAAK,EAAE;kBACNhC,eAAe,EAAEtD,aAAa;kBAC9B2B,YAAY,EAAE,MAAM;kBACpBR,KAAK,EAAE,MAAM;kBACb5B,MAAM,EAAE;gBACT;cAAE;gBAAA8L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACV/O,OAAA,CAACzB,OAAO;UAACmR,KAAK,EAAElP,eAAe,GAAGI,SAAS,CAAC,2CAA2C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;UAAAgN,QAAA,eACtH5N,OAAA,CAAChC,GAAG;YAAC8R,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChC5N,OAAA,CAAC7B,UAAU;cACVmQ,OAAO,EAAErF,uBAAwB;cACjC6H,IAAI,EAAC,OAAO;cACZC,QAAQ,EAAEvQ,eAAgB;cAAAoN,QAAA,eAE1B5N,OAAA;gBACCiP,uBAAuB,EAAE;kBAAEC,MAAM,EAAEhQ;gBAAS,CAAE;gBAC9C2J,KAAK,EAAE;kBAAEgH,OAAO,EAAErP,eAAe,GAAG,GAAG,GAAG;gBAAE;cAAE;gBAAAoO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACV/O,OAAA,CAACzB,OAAO;UAACmR,KAAK,EAAE9O,SAAS,CAAC,gBAAgB,CAAE;UAAAgN,QAAA,eAE5C5N,OAAA,CAAChC,GAAG;YAAC8R,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChC5N,OAAA,CAAC7B,UAAU;cACVmQ,OAAO,EAAExF,mBAAoB;cAC7BgI,IAAI,EAAC,OAAO;cAAAlD,QAAA,eAEZ5N,OAAA;gBAAMiP,uBAAuB,EAAE;kBAAEC,MAAM,EAAE/P;gBAAW;cAAE;gBAAAyP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GACP,IAAI,EAEP9L,WAAW,iBACVjD,OAAA,CAACF,0BAA0B;MAACwC,MAAM,EAAEW,WAAY;MAAC6O,gBAAgB,EAAEA,CAAA,KAAM5O,YAAY,CAAC,KAAK,CAAE;MAAC6O,aAAa,EAAE/K,wBAAyB;MAAC5D,eAAe,EAAEA,eAAgB;MAACD,YAAY,EAAEA,YAAa;MAACiE,kBAAkB,EAAEA,kBAAmB;MAAC3D,cAAc,EAAEA;IAAe;MAAAmL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAC7Q,eAEF/O,OAAA,CAAC9B,OAAO;MACPyJ,IAAI,EAAEC,eAAgB;MACtB4I,QAAQ,EAAE/N,mBAAoB;MAC9B0N,OAAO,EAAEjH,sBAAuB;MAChCkH,YAAY,EAAE;QACbC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACF0B,eAAe,EAAE;QAChB3B,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MAAA1C,QAAA,eAEF5N,OAAA,CAAChC,GAAG;QAAA4P,QAAA,gBACH5N,OAAA,CAACH,YAAY;UACZuJ,KAAK,EAAEV,qBAAsB;UAC7B6G,QAAQ,EAAEpG;QAAkB;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACA/O,OAAA;UAAA4N,QAAA,EACF;AACL;AACA;AACA;AACA;QAAK;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACT,CAAC;AAEL,CAAC;AAACtO,EAAA,CA5iCIN,YAAY;EAAA,QACQvB,cAAc,EAYnCW,cAAc;AAAA;AAAA0S,EAAA,GAbb9R,YAAY;AA8iClB,eAAeA,YAAY;AAAC,IAAA8R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}