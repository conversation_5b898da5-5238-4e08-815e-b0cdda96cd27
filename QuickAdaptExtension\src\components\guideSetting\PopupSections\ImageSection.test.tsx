// Unit test for enhanced positioning logic - handles 3+ sections with scrolling
describe('ImageSection Positioning Logic', () => {
  // Mock window dimensions
  beforeEach(() => {
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 800,
    });
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1200,
    });

    // Mock DOM elements for GuidePopUp Dialog
    const mockDialogElement = {
      getBoundingClientRect: () => ({
        top: 100,
        left: 200,
        width: 500,
        height: 400 // Max height when scrolling is active
      })
    };

    // Mock querySelector to return our mock dialog element
    global.document.querySelector = jest.fn((selector) => {
      if (selector === '.qadpt-guide-popup .MuiDialog-paper') {
        return mockDialogElement;
      }
      return null;
    });
  });

  // Mock DOM element for GuidePopUp
  const mockGuidePopup = {
    getBoundingClientRect: () => ({
      top: 100,
      left: 200,
      width: 500,
      height: 300
    })
  };

  // Test the enhanced positioning calculation logic for 3+ sections
  test('calculates correct position with sufficient space - uses Dialog container positioning', () => {
    const guidePopupRect = mockGuidePopup.getBoundingClientRect();

    // Enhanced positioning logic: uses actual Dialog container (not scrollable content)
    const popoverHeight = 44;
    const preferredGap = 10;
    const minTopMargin = 8;

    // Mock dialog element positioning (simulates 3+ sections with maxHeight)
    const dialogRect = {
      top: 100,
      left: 200,
      width: 500,
      height: 400 // Fixed height when scrolling
    };

    const idealTopPosition = dialogRect.top - popoverHeight - preferredGap;
    let expectedTop;

    if (idealTopPosition >= minTopMargin) {
      // Sufficient space: use ideal position relative to Dialog container
      expectedTop = idealTopPosition;
    }

    const expectedLeft = dialogRect.left + (dialogRect.width / 2) - 250; // Centered horizontally

    expect(expectedTop).toBe(46); // 100 - 44 - 10 = 46 (sufficient space)
    expect(expectedLeft).toBe(200); // 200 + 250 - 250
  });

  test('calculates correct position for settings popover to the right of GuidePopUp', () => {
    const guidePopupRect = mockGuidePopup.getBoundingClientRect();

    // Expected positioning logic from our implementation:
    // Settings popover should appear 10px to the right of the GuidePopUp, centered vertically
    const expectedTop = guidePopupRect.top + (guidePopupRect.height / 2) - 100;
    const expectedLeft = guidePopupRect.left + guidePopupRect.width + 10;

    expect(expectedTop).toBe(150); // 100 + (300/2) - 100 = 100 + 150 - 100 = 150
    expect(expectedLeft).toBe(710); // 200 + 500 + 10
  });

  test('handles different GuidePopUp dimensions correctly', () => {
    const differentGuidePopup = {
      getBoundingClientRect: () => ({
        top: 50,
        left: 150,
        width: 400,
        height: 400 // Maximum height
      })
    };

    const guidePopupRect = differentGuidePopup.getBoundingClientRect();

    // Image popover positioning
    const imageTop = guidePopupRect.top - 54;
    const imageLeft = guidePopupRect.left + (guidePopupRect.width / 2) - 250;

    // Settings popover positioning
    const settingsTop = guidePopupRect.top + (guidePopupRect.height / 2) - 100;
    const settingsLeft = guidePopupRect.left + guidePopupRect.width + 10;

    expect(imageTop).toBe(-4); // 50 - 54
    expect(imageLeft).toBe(100); // 150 + 200 - 250
    expect(settingsTop).toBe(150); // 50 + (400/2) - 100 = 50 + 200 - 100 = 150
    expect(settingsLeft).toBe(560); // 150 + 400 + 10
  });

  test('positioning logic handles edge cases', () => {
    // Test with minimal GuidePopUp dimensions
    const minimalGuidePopup = {
      getBoundingClientRect: () => ({
        top: 0,
        left: 0,
        width: 300,
        height: 200
      })
    };

    const guidePopupRect = minimalGuidePopup.getBoundingClientRect();

    // Image popover positioning with viewport constraint
    const popoverHeight = 44;
    const gap = 10;
    let imageTop = guidePopupRect.top - popoverHeight - gap;
    if (imageTop < 10) {
      imageTop = 10; // Minimum 10px from top of viewport
    }

    const imageLeft = guidePopupRect.left + (guidePopupRect.width / 2) - 250;
    const settingsTop = guidePopupRect.top + (guidePopupRect.height / 2) - 100;
    const settingsLeft = guidePopupRect.left + guidePopupRect.width + 10;

    expect(imageTop).toBe(10); // Should be constrained to 10px from top
    expect(imageLeft).toBe(-100); // 0 + 150 - 250
    expect(settingsTop).toBe(0); // 0 + (200/2) - 100
    expect(settingsLeft).toBe(310); // 0 + 300 + 10
  });

  test('handles limited space - maintains consistent 10px gap with minimum visibility', () => {
    // Test when GuidePopUp is close to top - maintains 10px gap but ensures minimum visibility
    const constrainedGuidePopup = {
      getBoundingClientRect: () => ({
        top: 35, // Limited space
        left: 200,
        width: 500,
        height: 300
      })
    };

    const guidePopupRect = constrainedGuidePopup.getBoundingClientRect();

    // Enhanced positioning logic - ALWAYS maintain 10px gap
    const popoverHeight = 44;
    const preferredGap = 10;
    const minTopMargin = 8;

    const idealTopPosition = guidePopupRect.top - popoverHeight - preferredGap;
    let imageTop;

    if (idealTopPosition >= minTopMargin) {
      imageTop = idealTopPosition;
    } else {
      // Constrained space: still maintain 10px gap, but ensure minimum visibility
      const positionWithFullGap = guidePopupRect.top - popoverHeight - preferredGap;
      imageTop = Math.max(minTopMargin, positionWithFullGap);
    }

    // 35 - 44 - 10 = -19 < 8, so use minTopMargin = 8
    expect(imageTop).toBe(8); // Should use minimum margin when 10px gap would be too close to top
  });

  test('handles moderate constraint - maintains 10px gap when possible', () => {
    // Test when there's enough space to position above with full 10px gap
    const moderateConstraintGuidePopup = {
      getBoundingClientRect: () => ({
        top: 62, // Enough space for 10px gap above minimum margin
        left: 200,
        width: 500,
        height: 300
      })
    };

    const guidePopupRect = moderateConstraintGuidePopup.getBoundingClientRect();

    const popoverHeight = 44;
    const preferredGap = 10;
    const minTopMargin = 8;

    const idealTopPosition = guidePopupRect.top - popoverHeight - preferredGap;
    let imageTop;

    if (idealTopPosition >= minTopMargin) {
      imageTop = idealTopPosition;
    } else {
      // Constrained space: still maintain 10px gap, but ensure minimum visibility
      const positionWithFullGap = guidePopupRect.top - popoverHeight - preferredGap;
      imageTop = Math.max(minTopMargin, positionWithFullGap);
    }

    // 62 - 44 - 10 = 8 >= 8, so use ideal position with full 10px gap
    expect(imageTop).toBe(8); // Should maintain full 10px gap
  });

  test('maintains consistent 10px gap when adequate viewport space is available', () => {
    // Test enhanced consistent gap logic - maintains 10px gap when there's adequate space
    const wellPositionedGuidePopup = {
      getBoundingClientRect: () => ({
        top: 80, // Plenty of space above
        left: 200,
        width: 500,
        height: 300
      })
    };

    const guidePopupRect = wellPositionedGuidePopup.getBoundingClientRect();

    const popoverHeight = 44;
    const consistentGap = 10; // Target consistent gap
    const minTopMargin = 8;

    // Calculate position that maintains the consistent 10px gap
    const positionWithConsistentGap = guidePopupRect.top - popoverHeight - consistentGap;
    const hasAdequateSpace = positionWithConsistentGap >= minTopMargin;

    let imageTop;
    if (hasAdequateSpace) {
      // Sufficient space: maintain consistent 10px gap
      imageTop = positionWithConsistentGap;
    }

    // 80 - 44 - 10 = 26 >= 8, so maintain consistent 10px gap
    expect(imageTop).toBe(26); // Should maintain consistent 10px gap
    expect(hasAdequateSpace).toBe(true); // Verify adequate space detection
  });

  test('follows fallback logic when viewport space is limited (10px → 5px → 2px)', () => {
    // Test fallback logic when there's limited viewport space above
    const constrainedGuidePopup = {
      getBoundingClientRect: () => ({
        top: 25, // Limited space above (only 25px from top)
        left: 200,
        width: 500,
        height: 300
      })
    };

    const guidePopupRect = constrainedGuidePopup.getBoundingClientRect();
    const popoverHeight = 44;
    const consistentGap = 10;
    const minTopMargin = 8;

    // Calculate position with consistent gap
    const positionWithConsistentGap = guidePopupRect.top - popoverHeight - consistentGap;
    const hasAdequateSpace = positionWithConsistentGap >= minTopMargin;

    let imageTop;
    if (hasAdequateSpace) {
      imageTop = positionWithConsistentGap;
    } else {
      // Limited space: follow fallback logic
      const availableSpaceAbove = guidePopupRect.top - popoverHeight - minTopMargin;

      if (availableSpaceAbove >= 10) {
        imageTop = positionWithConsistentGap;
      } else if (availableSpaceAbove >= 5) {
        // Fallback to 5px gap
        imageTop = guidePopupRect.top - popoverHeight - 5;
      } else if (availableSpaceAbove >= 2) {
        // Minimum 2px gap
        imageTop = guidePopupRect.top - popoverHeight - 2;
      } else {
        // Extreme constraint: ensure visibility
        imageTop = minTopMargin;
      }
    }

    // 25 - 44 - 10 = -29 < 8, so inadequate space
    // availableSpaceAbove = 25 - 44 - 8 = -27 < 5, so fallback to minimum
    expect(hasAdequateSpace).toBe(false);
    expect(imageTop).toBe(8); // Should fallback to minimum margin for visibility
  });

  test('handles 3+ image sections scenario - fixed positioning independent of scroll', () => {
    // Test specific scenario with 3+ image sections where GuidePopUp has fixed height and scrolling
    const scrollableGuidePopup = {
      getBoundingClientRect: () => ({
        top: 150, // GuidePopUp positioned lower due to content
        left: 300,
        width: 500,
        height: 400 // Fixed maxHeight with scrolling active
      })
    };

    // Mock the Dialog container positioning (what we actually use for positioning)
    const mockDialogRect = {
      top: 150,
      left: 300,
      width: 500,
      height: 400
    };

    const popoverHeight = 44;
    const consistentGap = 10;
    const minTopMargin = 8;

    // Use Dialog container positioning (independent of internal scroll state)
    const positionWithConsistentGap = mockDialogRect.top - popoverHeight - consistentGap;
    const hasAdequateSpace = positionWithConsistentGap >= minTopMargin;

    let imageTop;
    if (hasAdequateSpace) {
      imageTop = positionWithConsistentGap;
    }

    // 150 - 44 - 10 = 96 >= 8, so maintain consistent gap
    expect(imageTop).toBe(96); // Should position above Dialog container with consistent 10px gap
    expect(hasAdequateSpace).toBe(true);

    // Verify positioning is independent of GuidePopUp's internal scroll state
    const isIndependentOfScroll = imageTop === mockDialogRect.top - popoverHeight - consistentGap;
    expect(isIndependentOfScroll).toBe(true);
  });

  test('maintains 10px gap when GuidePopUp is small - prioritizes gap for small popups', () => {
    // Test scenario where GuidePopUp is small (height < 300px) - should always maintain 10px gap
    const smallGuidePopup = {
      getBoundingClientRect: () => ({
        top: 60, // Positioned higher, but limited viewport space above
        left: 200,
        width: 500,
        height: 200 // Small GuidePopUp height < 300px
      })
    };

    const guidePopupRect = smallGuidePopup.getBoundingClientRect();
    const popoverHeight = 44;
    const consistentGap = 10;
    const minTopMargin = 8;

    // Calculate position with 10px gap
    const positionWithConsistentGap = guidePopupRect.top - popoverHeight - consistentGap;

    // Even if viewport space is limited, small GuidePopUp should maintain 10px gap
    const isSmallGuidePopup = guidePopupRect.height < 300;
    const hasAdequateSpace = positionWithConsistentGap >= minTopMargin;

    let imageTop;
    if (hasAdequateSpace || isSmallGuidePopup) {
      imageTop = positionWithConsistentGap;
    }

    // 60 - 44 - 10 = 6, which is < 8 (minTopMargin), but since GuidePopUp is small (200 < 300),
    // it should still maintain the 10px gap
    expect(imageTop).toBe(6); // Should maintain 10px gap for small GuidePopUp
    expect(isSmallGuidePopup).toBe(true); // Verify it's considered small
  });

  test('maintains consistent gap when GuidePopUp position or height changes', () => {
    // Test dynamic repositioning when GuidePopUp moves or changes size

    // Initial position - GuidePopUp at top
    const initialGuidePopup = {
      getBoundingClientRect: () => ({
        top: 100,
        left: 200,
        width: 500,
        height: 200 // Smaller height (1-2 sections)
      })
    };

    // After change - GuidePopUp moves down and grows taller
    const changedGuidePopup = {
      getBoundingClientRect: () => ({
        top: 180, // Moved down
        left: 200,
        width: 500,
        height: 400 // Taller height (3+ sections)
      })
    };

    const popoverHeight = 44;
    const consistentGap = 10;
    const minTopMargin = 8;

    // Calculate initial position
    const initialRect = initialGuidePopup.getBoundingClientRect();
    const initialPosition = initialRect.top - popoverHeight - consistentGap;
    const initialHasSpace = initialPosition >= minTopMargin;

    // Calculate position after change
    const changedRect = changedGuidePopup.getBoundingClientRect();
    const changedPosition = changedRect.top - popoverHeight - consistentGap;
    const changedHasSpace = changedPosition >= minTopMargin;

    // Both scenarios should maintain consistent 10px gap
    expect(initialHasSpace).toBe(true);
    expect(changedHasSpace).toBe(true);
    expect(initialPosition).toBe(46); // 100 - 44 - 10 = 46
    expect(changedPosition).toBe(126); // 180 - 44 - 10 = 126

    // Verify gap is consistently maintained despite position/height changes
    const initialGap = initialRect.top - (initialPosition + popoverHeight);
    const changedGap = changedRect.top - (changedPosition + popoverHeight);
    expect(initialGap).toBe(10);
    expect(changedGap).toBe(10);
  });

  test('handles 2 image sections with smaller images - maintains consistent 10px gap', () => {
    // Test specific scenario: 2 image sections with reduced image sizes making GuidePopUp shorter
    const compactGuidePopup = {
      getBoundingClientRect: () => ({
        top: 180, // Positioned lower due to smaller content, indicating more space above
        left: 300,
        width: 500,
        height: 280 // Shorter than typical 3+ section height (< 350px)
      })
    };

    const mockDialogRect = {
      top: 180,
      left: 300,
      width: 500,
      height: 280
    };

    const popoverHeight = 44;
    const consistentGap = 10;
    const minTopMargin = 8;

    // Enhanced detection logic for compact GuidePopUp with extra space
    const isCompactGuidePopup = mockDialogRect.height < 350; // 280 < 350 = true
    const hasExtraSpaceAbove = mockDialogRect.top > 100; // 180 > 100 = true
    const positionWithConsistentGap = mockDialogRect.top - popoverHeight - consistentGap;
    const hasAdequateSpace = positionWithConsistentGap >= minTopMargin; // 126 >= 8 = true

    const shouldPrioritizeConsistentGap = hasAdequateSpace ||
      (isCompactGuidePopup && hasExtraSpaceAbove);

    let imageTop;
    if (shouldPrioritizeConsistentGap) {
      imageTop = positionWithConsistentGap;
    }

    // Verify the enhanced logic correctly identifies this scenario
    expect(isCompactGuidePopup).toBe(true); // Height 280 < 350
    expect(hasExtraSpaceAbove).toBe(true); // Top 180 > 100
    expect(shouldPrioritizeConsistentGap).toBe(true); // Should prioritize consistent gap

    // 180 - 44 - 10 = 126, which maintains exactly 10px gap
    expect(imageTop).toBe(126); // Should maintain consistent 10px gap utilizing available space
  });

  test('handles single image section scenario - maintains 10px gap with compact GuidePopUp', () => {
    // Test specific scenario with only 1 image section (compact GuidePopUp)
    const singleImageGuidePopup = {
      getBoundingClientRect: () => ({
        top: 200, // GuidePopUp positioned with adequate space above
        left: 300,
        width: 500,
        height: 250 // Compact height for single image section
      })
    };

    const guidePopupRect = singleImageGuidePopup.getBoundingClientRect();
    const popoverHeight = 44;
    const consistentGap = 10;
    const minTopMargin = 8;

    // Enhanced detection for compact GuidePopUp (single image section)
    const isCompactGuidePopup = guidePopupRect.height < 350; // 250 < 350 = true
    const hasExtraSpaceAbove = guidePopupRect.top > 100; // 200 > 100 = true
    const positionWithConsistentGap = guidePopupRect.top - popoverHeight - consistentGap;
    const hasAdequateSpace = positionWithConsistentGap >= minTopMargin; // 146 >= 8 = true

    const shouldPrioritizeConsistentGap = hasAdequateSpace ||
      (isCompactGuidePopup && hasExtraSpaceAbove);

    let imageTop;
    if (shouldPrioritizeConsistentGap) {
      imageTop = positionWithConsistentGap;
    }

    // Verify the logic correctly identifies single image section scenario
    expect(isCompactGuidePopup).toBe(true); // Height 250 < 350
    expect(hasExtraSpaceAbove).toBe(true); // Top 200 > 100
    expect(shouldPrioritizeConsistentGap).toBe(true); // Should prioritize consistent gap

    // 200 - 44 - 10 = 146, which maintains exactly 10px gap
    expect(imageTop).toBe(146); // Should maintain consistent 10px gap for single image section
  });

  // Test all your specific requirements
  test('Requirement 1: Single image section - always 10px gap regardless of height', () => {
    // Test single image section (taller or shorter) - always 10px gap
    const singleImageGuidePopup = {
      getBoundingClientRect: () => ({
        top: 180, // Adequate space above
        left: 300,
        width: 500,
        height: 220 // Single image height (can be taller or shorter)
      })
    };

    const guidePopupRect = singleImageGuidePopup.getBoundingClientRect();
    const popoverHeight = 44;
    const consistentGap = 10;
    const minTopMargin = 8;

    const positionWithConsistentGap = guidePopupRect.top - popoverHeight - consistentGap;
    const hasAdequateSpace = positionWithConsistentGap >= minTopMargin; // 126 >= 8 = true

    let imageTop;
    if (hasAdequateSpace) {
      imageTop = positionWithConsistentGap;
    }

    expect(hasAdequateSpace).toBe(true);
    expect(imageTop).toBe(126); // 180 - 44 - 10 = 126, maintains 10px gap
  });

  test('Requirement 2: Two image sections both smaller - always 10px gap', () => {
    // Test 2 image sections when both become smaller - always 10px gap
    const twoImagesSmallerGuidePopup = {
      getBoundingClientRect: () => ({
        top: 160, // Adequate space above
        left: 300,
        width: 500,
        height: 260 // Height when both images are smaller
      })
    };

    const guidePopupRect = twoImagesSmallerGuidePopup.getBoundingClientRect();
    const popoverHeight = 44;
    const consistentGap = 10;
    const minTopMargin = 8;

    const positionWithConsistentGap = guidePopupRect.top - popoverHeight - consistentGap;
    const hasAdequateSpace = positionWithConsistentGap >= minTopMargin; // 106 >= 8 = true

    let imageTop;
    if (hasAdequateSpace) {
      imageTop = positionWithConsistentGap;
    }

    expect(hasAdequateSpace).toBe(true);
    expect(imageTop).toBe(106); // 160 - 44 - 10 = 106, maintains 10px gap
  });

  test('Requirement 3a: Two image sections both taller - works as current with 10px gap', () => {
    // Test 2 image sections when both grow taller - works as current functionality
    const twoImagesTallerGuidePopup = {
      getBoundingClientRect: () => ({
        top: 140, // Adequate space above
        left: 300,
        width: 500,
        height: 340 // Height when both images are taller
      })
    };

    const guidePopupRect = twoImagesTallerGuidePopup.getBoundingClientRect();
    const popoverHeight = 44;
    const consistentGap = 10;
    const minTopMargin = 8;

    const positionWithConsistentGap = guidePopupRect.top - popoverHeight - consistentGap;
    const hasAdequateSpace = positionWithConsistentGap >= minTopMargin; // 86 >= 8 = true

    let imageTop;
    if (hasAdequateSpace) {
      imageTop = positionWithConsistentGap;
    }

    expect(hasAdequateSpace).toBe(true);
    expect(imageTop).toBe(86); // 140 - 44 - 10 = 86, maintains 10px gap
  });

  test('Requirement 3b: Two image sections mixed heights - works as current with 10px gap', () => {
    // Test 2 image sections when one taller, one smaller - works as current functionality
    const twoImagesMixedGuidePopup = {
      getBoundingClientRect: () => ({
        top: 130, // Adequate space above
        left: 300,
        width: 500,
        height: 320 // Height with mixed image sizes
      })
    };

    const guidePopupRect = twoImagesMixedGuidePopup.getBoundingClientRect();
    const popoverHeight = 44;
    const consistentGap = 10;
    const minTopMargin = 8;

    const positionWithConsistentGap = guidePopupRect.top - popoverHeight - consistentGap;
    const hasAdequateSpace = positionWithConsistentGap >= minTopMargin; // 76 >= 8 = true

    let imageTop;
    if (hasAdequateSpace) {
      imageTop = positionWithConsistentGap;
    }

    expect(hasAdequateSpace).toBe(true);
    expect(imageTop).toBe(76); // 130 - 44 - 10 = 76, maintains 10px gap
  });

  test('Requirement 4: Three+ image sections - works as current functionality with 10px gap', () => {
    // Test 3+ image sections (taller or smaller) - works as current functionality
    const threeImagesGuidePopup = {
      getBoundingClientRect: () => ({
        top: 120, // Adequate space above
        left: 300,
        width: 500,
        height: 400 // Fixed maxHeight with scrolling (current functionality)
      })
    };

    const guidePopupRect = threeImagesGuidePopup.getBoundingClientRect();
    const popoverHeight = 44;
    const consistentGap = 10;
    const minTopMargin = 8;

    const positionWithConsistentGap = guidePopupRect.top - popoverHeight - consistentGap;
    const hasAdequateSpace = positionWithConsistentGap >= minTopMargin; // 66 >= 8 = true

    let imageTop;
    if (hasAdequateSpace) {
      imageTop = positionWithConsistentGap;
    }

    expect(hasAdequateSpace).toBe(true);
    expect(imageTop).toBe(66); // 120 - 44 - 10 = 66, maintains 10px gap (works as current)
  });
});
