{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\Imagesection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip, Snackbar, Alert } from \"@mui/material\";\nimport RemoveIcon from \"@mui/icons-material/Remove\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport { useTranslation } from 'react-i18next';\nimport { uploadfile, hyperlink, files, uploadicon, replaceimageicon, copyicon, deleteicon, sectionheight, Settings, CrossIcon } from \"../../../assets/icons/icons\";\nimport useDrawerStore, { IMG_CONTAINER_DEFAULT_HEIGHT, IMG_CONTAINER_MAX_HEIGHT, IMG_CONTAINER_MIN_HEIGHT, IMG_OBJECT_FIT, IMG_STEP_VALUE } from \"../../../store/drawerStore\";\nimport { ChromePicker } from \"react-color\";\nimport \"./PopupSections.css\";\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageSection = ({\n  setImageSrc,\n  setImageName,\n  onDelete,\n  onClone,\n  isCloneDisabled\n}) => {\n  _s();\n  var _imagesContainer$find;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    uploadImage,\n    imagesContainer,\n    imageAnchorEl,\n    setImageAnchorEl,\n    replaceImage,\n    cloneImageContainer,\n    deleteImageContainer,\n    updateImageContainer,\n    toggleFit,\n    setImageSrc: storeImageSrc\n  } = useDrawerStore(state => state);\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState('');\n  const [snackbarSeverity, setSnackbarSeverity] = useState('info');\n  const [snackbarKey, setSnackbarKey] = useState(0);\n  const openSnackbar = () => {\n    setSnackbarKey(prev => prev + 1);\n    setSnackbarOpen(true);\n  };\n  const closeSnackbar = () => {\n    setSnackbarOpen(false);\n  };\n  const [showHyperlinkInput, setShowHyperlinkInput] = useState({\n    currentContainerId: \"\",\n    isOpen: false\n  });\n  const [imageLink, setImageLink] = useState(\"\");\n  const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState(null);\n  const [currentImageSectionInfo, setCurrentImageSectionInfo] = useState({\n    currentContainerId: \"\",\n    isImage: false,\n    height: IMG_CONTAINER_DEFAULT_HEIGHT\n  });\n  const [selectedAction, setSelectedAction] = useState(\"none\");\n  const [isModelOpen, setModelOpen] = useState(false);\n  const [formOfUpload, setFormOfUpload] = useState(\"\");\n  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);\n  const [selectedColor, setSelectedColor] = useState(\"#313030\");\n  const [isReplaceImage, setReplaceImage] = useState(false);\n  const guidePopupRef = useRef(null);\n  const [popoverPositions, setPopoverPositions] = useState({\n    imagePopover: {},\n    settingsPopover: {}\n  });\n  const openSettingsPopover = Boolean(settingsAnchorEl);\n  const handleActionChange = event => {\n    setSelectedAction(event.target.value);\n  };\n  const handleSettingsClick = event => {\n    setSettingsAnchorEl(event.currentTarget);\n  };\n  const handleCloseSettingsPopover = () => {\n    setSettingsAnchorEl(null);\n  };\n  const imageContainerStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    padding: 0,\n    margin: 0,\n    overflow: \"hidden\"\n  };\n  const imageStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    margin: 0,\n    padding: 0,\n    borderRadius: \"0\"\n  };\n  const iconRowStyle = {\n    display: \"flex\",\n    justifyContent: \"center\",\n    gap: \"16px\",\n    marginTop: \"10px\"\n  };\n  const iconTextStyle = {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    width: \"100%\"\n  };\n  const handleImageUpload = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      var _event$target$files2;\n      const parts = file.name.split('.');\n      const extension = parts.pop();\n\n      // Check for double extensions (e.g. file.html.png) or missing/invalid extension\n      if (parts.length > 1 || !extension) {\n        setSnackbarMessage(\"Uploaded file name should not contain any special character\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      if (file.name.length > 128) {\n        setSnackbarMessage(\"File name should not exceed 128 characters\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      setImageName((_event$target$files2 = event.target.files) === null || _event$target$files2 === void 0 ? void 0 : _event$target$files2[0].name);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        const base64Image = reader.result;\n        storeImageSrc(base64Image);\n        setImageSrc(base64Image);\n        uploadImage(imageAnchorEl.containerId, {\n          altText: file.name,\n          id: crypto.randomUUID(),\n          url: base64Image,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n    setModelOpen(false);\n  };\n  const handleImageUploadFormApp = file => {\n    if (file) {\n      storeImageSrc(file.Url);\n      setImageSrc(file.Url);\n      if (isReplaceImage) {\n        replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\n          altText: file.FileName,\n          id: imageAnchorEl.buttonId,\n          url: file.Url,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n        setReplaceImage(false);\n      } else {\n        uploadImage(imageAnchorEl.containerId, {\n          altText: file.FileName,\n          id: crypto.randomUUID(),\n          // Use existing ID\n          url: file.Url,\n          // Directly use the URL\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      }\n    }\n    setModelOpen(false);\n  };\n  const handleReplaceImage = event => {\n    var _event$target$files3;\n    const file = (_event$target$files3 = event.target.files) === null || _event$target$files3 === void 0 ? void 0 : _event$target$files3[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\n          altText: file.name,\n          id: imageAnchorEl.buttonId,\n          url: reader.result,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleClick = (event, containerId, imageId, isImage, currentHeight) => {\n    // @ts-ignore\n    if ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\n    setImageAnchorEl({\n      buttonId: imageId,\n      containerId: containerId,\n      // @ts-ignore\n      value: event.currentTarget\n    });\n    setSettingsAnchorEl(null);\n    setCurrentImageSectionInfo({\n      currentContainerId: containerId,\n      isImage,\n      height: currentHeight\n    });\n    setShowHyperlinkInput({\n      currentContainerId: \"\",\n      isOpen: false\n    });\n  };\n  const handleClose = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      // @ts-ignore\n      value: null\n    });\n  };\n  const open = Boolean(imageAnchorEl.value);\n  const colorPickerOpen = Boolean(colorPickerAnchorEl);\n  const id = open ? \"image-popover\" : undefined;\n  const handleIncreaseHeight = prevHeight => {\n    if (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\n    const newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const handleDecreaseHeight = prevHeight => {\n    if (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\n    const newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const triggerImageUpload = () => {\n    var _document$getElementB;\n    (_document$getElementB = document.getElementById(\"replace-upload\")) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.click();\n    // setModelOpen(true);\n    // setReplaceImage(true);\n  };\n  const currentContainerColor = ((_imagesContainer$find = imagesContainer.find(item => item.id === imageAnchorEl.containerId)) === null || _imagesContainer$find === void 0 ? void 0 : _imagesContainer$find.style.backgroundColor) || \"transparent\";\n  // Function to delete the section\n  const handleDeleteSection = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      // @ts-ignore\n      value: null\n    });\n\n    // Call the delete function from the store\n    deleteImageContainer(imageAnchorEl.containerId);\n\n    // Call the onDelete callback if provided\n    if (onDelete) {\n      onDelete();\n    }\n  };\n  const handleLinkSubmit = event => {\n    if (event.key === \"Enter\" && imageLink) {\n      uploadImage(imageAnchorEl.containerId, {\n        altText: \"New Image\",\n        id: crypto.randomUUID(),\n        url: imageLink,\n        backgroundColor: \"transparent\",\n        objectFit: IMG_OBJECT_FIT\n      });\n      setShowHyperlinkInput({\n        currentContainerId: \"\",\n        isOpen: false\n      });\n    }\n  };\n  const handleCloneImgContainer = () => {\n    // Check if cloning is disabled due to section limits\n    if (isCloneDisabled) {\n      return; // Don't clone if limit is reached\n    }\n\n    // Call the clone function from the store\n    cloneImageContainer(imageAnchorEl.containerId);\n\n    // Call the onClone callback if provided\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleCloseColorPicker = () => {\n    setColorPickerAnchorEl(null);\n  };\n  const handleColorChange = color => {\n    setSelectedColor(color.hex);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      backgroundColor: color.hex\n    });\n  };\n  const handleBackgroundColorClick = event => {\n    setColorPickerAnchorEl(event.currentTarget);\n  };\n\n  // Constants for positioning calculations\n  const POSITIONING_CONSTANTS = {\n    IMAGE_POPOVER: {\n      HEIGHT: 40,\n      WIDTH: 500,\n      REQUIRED_GAP: 10,\n      FALLBACK_GAPS: [10, 5, 2]\n    },\n    SETTINGS_POPOVER: {\n      HEIGHT: 200,\n      WIDTH: 300,\n      GAP: 10\n    },\n    VIEWPORT: {\n      MIN_TOP_MARGIN: 8,\n      MIN_SIDE_MARGIN: 10\n    },\n    Z_INDEX: 999999\n  };\n\n  // Cached DOM selectors for performance\n  const getDOMElements = () => ({\n    dialog: document.querySelector('.qadpt-guide-popup .MuiDialog-paper'),\n    guidePopup: document.getElementById('guide-popup'),\n    perfectScrollbar: document.querySelector('.qadpt-guide-popup .ps')\n  });\n\n  // Function to get GuidePopUp Dialog container position for consistent positioning\n  const getGuidePopupPosition = () => {\n    const {\n      dialog,\n      guidePopup\n    } = getDOMElements();\n\n    // Always use the Dialog container for consistent positioning\n    const element = dialog || guidePopup;\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      return {\n        top: rect.top,\n        left: rect.left,\n        width: rect.width,\n        height: rect.height\n      };\n    }\n    return null;\n  };\n\n  // Helper function to calculate gap-based positioning\n  const calculateGapPosition = (guideTop, popoverHeight, requiredGap, minTopMargin) => {\n    const positionWithGap = guideTop - popoverHeight - requiredGap;\n    if (positionWithGap >= minTopMargin) {\n      return positionWithGap;\n    }\n\n    // Try fallback gaps in order of preference\n    for (const gap of POSITIONING_CONSTANTS.IMAGE_POPOVER.FALLBACK_GAPS) {\n      const availableSpace = guideTop - popoverHeight - minTopMargin;\n      if (availableSpace >= gap) {\n        return guideTop - popoverHeight - gap;\n      }\n    }\n\n    // Extreme constraint fallback\n    return Math.max(minTopMargin, guideTop - popoverHeight - POSITIONING_CONSTANTS.IMAGE_POPOVER.FALLBACK_GAPS[2]);\n  };\n\n  // Helper function to constrain position within viewport bounds\n  const constrainToViewport = (position, size, viewportSize, margin) => {\n    return Math.max(margin, Math.min(position, viewportSize - size - margin));\n  };\n\n  // Enhanced positioning for image properties popover - handles 3+ sections with scrolling\n  const getImagePopoverPosition = () => {\n    const guidePopupPos = getGuidePopupPosition();\n    if (!guidePopupPos) return {};\n    const {\n      HEIGHT: popoverHeight,\n      WIDTH: popoverWidth,\n      REQUIRED_GAP\n    } = POSITIONING_CONSTANTS.IMAGE_POPOVER;\n    const {\n      MIN_TOP_MARGIN,\n      MIN_SIDE_MARGIN\n    } = POSITIONING_CONSTANTS.VIEWPORT;\n    const {\n      innerHeight: viewportHeight,\n      innerWidth: viewportWidth\n    } = window;\n\n    // Calculate top position with gap logic\n    let topPosition = calculateGapPosition(guidePopupPos.top, popoverHeight, REQUIRED_GAP, MIN_TOP_MARGIN);\n\n    // Final safety check for viewport bottom\n    topPosition = Math.min(topPosition, viewportHeight - popoverHeight);\n\n    // Calculate horizontal position - center relative to GuidePopUp\n    const centerOffset = popoverWidth / 2;\n    let leftPosition = guidePopupPos.left + guidePopupPos.width / 2 - centerOffset;\n\n    // Constrain to viewport bounds\n    leftPosition = constrainToViewport(leftPosition, popoverWidth, viewportWidth, MIN_SIDE_MARGIN);\n    return {\n      top: topPosition,\n      left: leftPosition,\n      position: 'fixed',\n      zIndex: POSITIONING_CONSTANTS.Z_INDEX\n    };\n  };\n\n  // Enhanced positioning for settings popup - maintains consistent relative positioning\n  const getSettingsPopoverPosition = () => {\n    const guidePopupPos = getGuidePopupPosition();\n    if (!guidePopupPos) return {};\n    const {\n      HEIGHT: settingsHeight,\n      WIDTH: settingsWidth,\n      GAP\n    } = POSITIONING_CONSTANTS.SETTINGS_POPOVER;\n    const {\n      MIN_SIDE_MARGIN\n    } = POSITIONING_CONSTANTS.VIEWPORT;\n    const {\n      innerHeight: viewportHeight,\n      innerWidth: viewportWidth\n    } = window;\n\n    // Calculate position to the right of GuidePopUp with consistent spacing\n    let leftPosition = guidePopupPos.left + guidePopupPos.width + GAP;\n    let topPosition = guidePopupPos.top + guidePopupPos.height / 2 - settingsHeight / 2 + GAP;\n\n    // Handle viewport constraints - if not enough space to the right, position to the left\n    if (leftPosition + settingsWidth > viewportWidth - MIN_SIDE_MARGIN) {\n      leftPosition = guidePopupPos.left - settingsWidth - GAP;\n    }\n\n    // Constrain to viewport bounds\n    topPosition = constrainToViewport(topPosition, settingsHeight, viewportHeight, MIN_SIDE_MARGIN);\n    leftPosition = constrainToViewport(leftPosition, settingsWidth, viewportWidth, MIN_SIDE_MARGIN);\n    return {\n      top: topPosition,\n      left: leftPosition,\n      position: 'fixed',\n      zIndex: POSITIONING_CONSTANTS.Z_INDEX\n    };\n  };\n\n  // Update popover positions\n  const updatePopoverPositions = () => {\n    setPopoverPositions({\n      imagePopover: getImagePopoverPosition(),\n      settingsPopover: getSettingsPopoverPosition()\n    });\n  };\n\n  // Optimized event handlers with debouncing\n  const createDebouncedHandler = (handler, delay = 16) => {\n    let timeoutId;\n    return () => {\n      clearTimeout(timeoutId);\n      timeoutId = setTimeout(handler, delay);\n    };\n  };\n\n  // Effect to handle responsive positioning\n  useEffect(() => {\n    const debouncedUpdate = createDebouncedHandler(updatePopoverPositions);\n\n    // Update positions when popovers are opened\n    if (open || openSettingsPopover) {\n      updatePopoverPositions();\n\n      // Set up periodic position verification with optimized interval\n      const positionCheckInterval = setInterval(() => {\n        if (open || openSettingsPopover) {\n          updatePopoverPositions();\n        } else {\n          clearInterval(positionCheckInterval);\n        }\n      }, 200);\n\n      // Store interval ID for cleanup\n      window.qadptPositionCheckInterval = positionCheckInterval;\n    }\n\n    // Get DOM elements once for efficiency\n    const {\n      dialog,\n      guidePopup,\n      perfectScrollbar\n    } = getDOMElements();\n\n    // Add event listeners for window resize and scroll\n    window.addEventListener('resize', debouncedUpdate);\n    window.addEventListener('scroll', debouncedUpdate);\n\n    // Add scroll listener for PerfectScrollbar within GuidePopUp\n    if (perfectScrollbar) {\n      perfectScrollbar.addEventListener('ps-scroll-y', debouncedUpdate);\n      perfectScrollbar.addEventListener('scroll', debouncedUpdate);\n    }\n\n    // Optimized observers setup\n    let resizeObserver = null;\n    let mutationObserver = null;\n\n    // Enhanced ResizeObserver with change detection\n    if (window.ResizeObserver) {\n      const SIGNIFICANT_CHANGE_THRESHOLD = 1;\n      resizeObserver = new ResizeObserver(entries => {\n        let hasSignificantChange = false;\n        for (const entry of entries) {\n          const {\n            width,\n            height\n          } = entry.contentRect;\n          const element = entry.target;\n          const lastWidth = parseFloat(element.dataset.lastWidth || '0');\n          const lastHeight = parseFloat(element.dataset.lastHeight || '0');\n          if (Math.abs(width - lastWidth) > SIGNIFICANT_CHANGE_THRESHOLD || Math.abs(height - lastHeight) > SIGNIFICANT_CHANGE_THRESHOLD) {\n            hasSignificantChange = true;\n            element.dataset.lastWidth = width.toString();\n            element.dataset.lastHeight = height.toString();\n          }\n        }\n        if (hasSignificantChange) {\n          updatePopoverPositions();\n          // Debounced follow-up for stability\n          setTimeout(updatePopoverPositions, 50);\n        }\n      });\n\n      // Observe elements if they exist\n      [guidePopup, dialog, perfectScrollbar].forEach(element => {\n        if (element) resizeObserver.observe(element);\n      });\n    }\n\n    // Enhanced MutationObserver with targeted observation\n    if (guidePopup && window.MutationObserver) {\n      const LAYOUT_AFFECTING_ATTRIBUTES = ['style', 'class'];\n      mutationObserver = new MutationObserver(mutations => {\n        const shouldUpdate = mutations.some(mutation => mutation.type === 'childList' || mutation.type === 'attributes' && LAYOUT_AFFECTING_ATTRIBUTES.includes(mutation.attributeName || ''));\n        if (shouldUpdate) {\n          setTimeout(updatePopoverPositions, 50);\n        }\n      });\n      mutationObserver.observe(guidePopup, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: LAYOUT_AFFECTING_ATTRIBUTES\n      });\n    }\n\n    // Cleanup function\n    return () => {\n      var _resizeObserver, _mutationObserver;\n      window.removeEventListener('resize', debouncedUpdate);\n      window.removeEventListener('scroll', debouncedUpdate);\n\n      // Remove PerfectScrollbar event listeners\n      if (perfectScrollbar) {\n        perfectScrollbar.removeEventListener('ps-scroll-y', debouncedUpdate);\n        perfectScrollbar.removeEventListener('scroll', debouncedUpdate);\n      }\n\n      // Cleanup periodic position check\n      const intervalId = window.qadptPositionCheckInterval;\n      if (intervalId) {\n        clearInterval(intervalId);\n        delete window.qadptPositionCheckInterval;\n      }\n\n      // Disconnect observers\n      (_resizeObserver = resizeObserver) === null || _resizeObserver === void 0 ? void 0 : _resizeObserver.disconnect();\n      (_mutationObserver = mutationObserver) === null || _mutationObserver === void 0 ? void 0 : _mutationObserver.disconnect();\n    };\n  }, [open, openSettingsPopover]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [imagesContainer.map(item => {\n      var _item$images$, _item$images$2, _item$images$3, _item$style;\n      const imageSrc = (_item$images$ = item.images[0]) === null || _item$images$ === void 0 ? void 0 : _item$images$.url;\n      const imageId = (_item$images$2 = item.images[0]) === null || _item$images$2 === void 0 ? void 0 : _item$images$2.id;\n      const objectFit = ((_item$images$3 = item.images[0]) === null || _item$images$3 === void 0 ? void 0 : _item$images$3.objectFit) || IMG_OBJECT_FIT;\n      const currentSecHeight = (item === null || item === void 0 ? void 0 : (_item$style = item.style) === null || _item$style === void 0 ? void 0 : _item$style.height) || IMG_CONTAINER_DEFAULT_HEIGHT;\n      const id = item.id;\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: \"100%\",\n          height: \"100%\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          justifyContent: \"flex-start\",\n          alignItems: \"center\",\n          // padding: \"5px\",\n          margin: \"0px\",\n          overflow: \"auto\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            ...imageContainerStyle,\n            backgroundColor: item.style.backgroundColor,\n            height: `${item.style.height}px`\n          },\n          onClick: e => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight),\n          component: \"div\",\n          id: id,\n          onMouseOver: () => {\n            setImageAnchorEl({\n              buttonId: imageId,\n              containerId: id,\n              value: null\n            });\n          },\n          children: imageSrc ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: imageSrc,\n            alt: \"Uploaded\",\n            style: {\n              ...imageStyle,\n              objectFit\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 9\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: \"center\",\n              width: \"100%\",\n              height: \"100%\",\n              display: \"flex\",\n              flexDirection: \"column\",\n              justifyContent: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: iconTextStyle,\n              component: \"div\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: uploadfile\n                },\n                style: {\n                  display: \"inline-block\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                align: \"center\",\n                sx: {\n                  fontSize: \"14px\",\n                  fontWeight: \"600\"\n                },\n                children: translate(\"Upload file\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              align: \"center\",\n              color: \"textSecondary\",\n              sx: {\n                fontSize: \"14px\"\n              },\n              children: translate(\"Drag & Drop to upload file\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              align: \"center\",\n              color: \"textSecondary\",\n              sx: {\n                marginTop: \"8px\",\n                fontSize: \"14px\"\n              },\n              children: translate(\"Or\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 10\n            }, this), showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? /*#__PURE__*/_jsxDEV(TextField, {\n              value: imageLink,\n              onChange: e => setImageLink(e.target.value),\n              onKeyDown: handleLinkSubmit,\n              autoFocus: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 11\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: iconRowStyle,\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: translate(\"Coming soon\"),\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    pointerEvents: \"auto\",\n                    cursor: \"pointer\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: hyperlink\n                    },\n                    style: {\n                      color: \"black\",\n                      cursor: \"pointer\",\n                      fontSize: \"32px\",\n                      opacity: \"0.5\",\n                      pointerEvents: \"none\"\n                    },\n                    id: \"hyperlink\",\n                    className: \"qadpt-image-upload\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 726,\n                    columnNumber: 5\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 3\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: translate(\"Coming soon\"),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  onClick: event => {\n                    //setModelOpen(true);\n                  },\n                  dangerouslySetInnerHTML: {\n                    __html: files\n                  },\n                  style: {\n                    color: \"black\",\n                    cursor: \"pointer\",\n                    fontSize: \"32px\",\n                    opacity: \"0.5\"\n                  },\n                  id: \"folder\",\n                  className: \"qadpt-image-upload\"\n                  //title=\"Coming Soon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: translate(\"Upload File\"),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  onClick: event => {\n                    var _document$getElementB2;\n                    event === null || event === void 0 ? void 0 : event.stopPropagation();\n                    (_document$getElementB2 = document.getElementById(\"file-upload\")) === null || _document$getElementB2 === void 0 ? void 0 : _document$getElementB2.click();\n                  },\n                  id: \"file-upload1\",\n                  className: \"qadpt-image-upload\",\n                  dangerouslySetInnerHTML: {\n                    __html: uploadicon\n                  },\n                  style: {\n                    color: \"black\",\n                    cursor: \"pointer\",\n                    fontSize: \"32px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 12\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"file-upload\",\n                style: {\n                  display: \"none\"\n                },\n                accept: \"image/*\",\n                onChange: handleImageUpload\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 12\n              }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n                open: snackbarOpen,\n                autoHideDuration: 3000,\n                onClose: closeSnackbar,\n                anchorOrigin: {\n                  vertical: 'bottom',\n                  horizontal: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(Alert, {\n                  onClose: closeSnackbar,\n                  severity: snackbarSeverity,\n                  sx: {\n                    width: '100%'\n                  },\n                  children: snackbarMessage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 13\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 12\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 7\n        }, this)\n      }, id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 634,\n        columnNumber: 6\n      }, this);\n    }), Boolean(imageAnchorEl.value) ? /*#__PURE__*/_jsxDEV(Popover, {\n      className: \"qadpt-imgsec-popover\",\n      id: id,\n      open: open,\n      anchorEl: null,\n      onClose: handleClose,\n      anchorReference: \"none\",\n      slotProps: {\n        paper: {\n          style: {\n            ...popoverPositions.imagePopover,\n            height: 'auto',\n            width: 'auto',\n            padding: '5px 10px',\n            marginLeft: 'auto',\n            marginRight: 'auto'\n          }\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"15px\",\n          height: \"100%\",\n          padding: \"0 10px\",\n          fontSize: \"12px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\"\n          },\n          children: currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId && currentImageSectionInfo.isImage ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: replaceimageicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              fontSize: \"12px\",\n              marginLeft: \"5px\",\n              onClick: triggerImageUpload,\n              children: translate(\"Replace Image\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"replace-upload\",\n              style: {\n                display: \"none\"\n              },\n              accept: \"image/*\",\n              onChange: handleReplaceImage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 10\n            }, this)]\n          }, void 0, true) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-tool-items\",\n          sx: {\n            display: \"flex\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: sectionheight\n            },\n            style: {\n              display: \"flex\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\") : translate(\"Decrease height\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleDecreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 858,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            fontSize: \"12px\",\n            children: currentImageSectionInfo.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 862,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\") : translate(\"Increase height\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleIncreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 841,\n          columnNumber: 1\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Settings\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-tool-items\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: handleSettingsClick,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: Settings\n                  },\n                  style: {\n                    color: \"black\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 881,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Popover, {\n              className: \"qadpt-imgset\",\n              open: openSettingsPopover,\n              anchorEl: null,\n              onClose: handleCloseSettingsPopover,\n              anchorReference: \"none\",\n              slotProps: {\n                paper: {\n                  style: {\n                    ...popoverPositions.settingsPopover,\n                    width: \"205px\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                p: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    sx: {\n                      color: \"rgba(95, 158, 160, 1)\"\n                    },\n                    children: translate(\"Image Properties\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 916,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleCloseSettingsPopover,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: CrossIcon\n                      },\n                      style: {\n                        color: \"black\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 926,\n                      columnNumber: 12\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 922,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 911,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate(\"Coming soon\"),\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    mt: 2,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      sx: {\n                        marginBottom: \"10px\"\n                      },\n                      children: translate(\"Image Actions\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 934,\n                      columnNumber: 11\n                    }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                      select: true,\n                      fullWidth: true,\n                      variant: \"outlined\",\n                      size: \"small\",\n                      value: selectedAction,\n                      onChange: handleActionChange,\n                      sx: {\n                        \"& .MuiOutlinedInput-root\": {\n                          borderColor: \"rgba(246, 238, 238, 1)\"\n                        }\n                      },\n                      disabled: true,\n                      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"none\",\n                        children: translate(\"None\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 955,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"specificStep\",\n                        children: translate(\"Specific Step\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 956,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"openUrl\",\n                        children: translate(\"Open URL\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 957,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"clickElement\",\n                        children: translate(\"Click Element\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 958,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startTour\",\n                        children: translate(\"Start Tour\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 959,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startMicroSurvey\",\n                        children: translate(\"Start Micro Survey\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 960,\n                        columnNumber: 14\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 941,\n                      columnNumber: 11\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 933,\n                    columnNumber: 10\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 932,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: translate(\"Image Formatting\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 965,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    gap: 1,\n                    mt: 1,\n                    children: [\"Fill\", \"Fit\"].map(item => {\n                      // Get current image's objectFit to determine selected state\n                      const currentContainer = imagesContainer.find(c => c.id === imageAnchorEl.containerId);\n                      const currentImage = currentContainer === null || currentContainer === void 0 ? void 0 : currentContainer.images.find(img => img.id === imageAnchorEl.buttonId);\n                      const currentObjectFit = (currentImage === null || currentImage === void 0 ? void 0 : currentImage.objectFit) || IMG_OBJECT_FIT;\n\n                      // Determine if this button should be selected\n                      const isSelected = item === \"Fill\" && currentObjectFit === \"cover\" || item === \"Fit\" && currentObjectFit === \"contain\";\n                      return /*#__PURE__*/_jsxDEV(Button, {\n                        onClick: () => toggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item),\n                        variant: \"outlined\",\n                        size: \"small\",\n                        sx: {\n                          width: \"88.5px\",\n                          height: \"41px\",\n                          padding: \"10px 12px\",\n                          gap: \"12px\",\n                          borderRadius: \"6px 6px 6px 6px\",\n                          border: isSelected ? \"1px solid rgba(95, 158, 160, 1)\" : \"1px solid rgba(246, 238, 238, 1)\",\n                          backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\n                          backgroundBlendMode: \"multiply\",\n                          color: \"black\",\n                          \"&:hover\": {\n                            backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\"\n                          }\n                        },\n                        children: translate(item)\n                      }, item, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 987,\n                        columnNumber: 14\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 971,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 964,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 910,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 879,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Background Color\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleBackgroundColorClick,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  backgroundColor: selectedColor,\n                  borderRadius: \"100%\",\n                  width: \"20px\",\n                  height: \"20px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1030,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1026,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1025,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1024,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\") : translate(\"Clone Section\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleCloneImgContainer,\n              size: \"small\",\n              disabled: isCloneDisabled,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1042,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1041,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1040,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Delete Section\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleDeleteSection,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1058,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1057,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1055,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 808,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 788,\n      columnNumber: 5\n    }, this) : null, isModelOpen && /*#__PURE__*/_jsxDEV(SelectImageFromApplication, {\n      isOpen: isModelOpen,\n      handleModelClose: () => setModelOpen(false),\n      onImageSelect: handleImageUploadFormApp,\n      setFormOfUpload: setFormOfUpload,\n      formOfUpload: formOfUpload,\n      handleReplaceImage: handleReplaceImage,\n      isReplaceImage: isReplaceImage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1071,\n      columnNumber: 6\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      open: colorPickerOpen,\n      anchorEl: colorPickerAnchorEl,\n      onClose: handleCloseColorPicker,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(ChromePicker, {\n          color: currentContainerColor,\n          onChange: handleColorChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1088,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1092,\n          columnNumber: 8\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1087,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1074,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(ImageSection, \"phKmd8/vYJ2if4XJ7NWyWxDuMKo=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ImageSection;\nexport default ImageSection;\nvar _c;\n$RefreshReg$(_c, \"ImageSection\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "Typography", "Popover", "IconButton", "TextField", "MenuItem", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "RemoveIcon", "AddIcon", "useTranslation", "uploadfile", "hyperlink", "files", "uploadicon", "replaceimageicon", "copyicon", "deleteicon", "sectionheight", "Settings", "CrossIcon", "useDrawerStore", "IMG_CONTAINER_DEFAULT_HEIGHT", "IMG_CONTAINER_MAX_HEIGHT", "IMG_CONTAINER_MIN_HEIGHT", "IMG_OBJECT_FIT", "IMG_STEP_VALUE", "ChromePicker", "SelectImageFromApplication", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageSection", "setImageSrc", "setImageName", "onDelete", "onClone", "isCloneDisabled", "_s", "_imagesContainer$find", "t", "translate", "uploadImage", "imagesContainer", "imageAnchorEl", "setImageAnchorEl", "replaceImage", "cloneImageContainer", "deleteImageContainer", "updateImageContainer", "toggleFit", "storeImageSrc", "state", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "snackbarKey", "setSnackbarKey", "openSnackbar", "prev", "closeSnackbar", "showHyperlinkInput", "setShowHyperlinkInput", "currentContainerId", "isOpen", "imageLink", "setImageLink", "colorPickerAnchorEl", "setColorPickerAnchorEl", "currentImageSectionInfo", "setCurrentImageSectionInfo", "isImage", "height", "selectedAction", "setSelectedAction", "isModelOpen", "setModelOpen", "formOfUpload", "setFormOfUpload", "settingsAnchorEl", "setSettingsAnchorEl", "selectedColor", "setSelectedColor", "isReplaceImage", "setReplaceImage", "guidePopupRef", "popoverPositions", "setPopoverPositions", "imagePopover", "settingsPopover", "openSettingsPopover", "Boolean", "handleActionChange", "event", "target", "value", "handleSettingsClick", "currentTarget", "handleCloseSettingsPopover", "imageContainerStyle", "width", "display", "justifyContent", "alignItems", "padding", "margin", "overflow", "imageStyle", "borderRadius", "iconRowStyle", "gap", "marginTop", "iconTextStyle", "flexDirection", "handleImageUpload", "_event$target$files", "file", "_event$target$files2", "parts", "name", "split", "extension", "pop", "length", "reader", "FileReader", "onloadend", "base64Image", "result", "containerId", "altText", "id", "crypto", "randomUUID", "url", "backgroundColor", "objectFit", "readAsDataURL", "handleImageUploadFormApp", "Url", "buttonId", "FileName", "handleReplaceImage", "_event$target$files3", "handleClick", "imageId", "currentHeight", "includes", "handleClose", "open", "colorPickerOpen", "undefined", "handleIncreaseHeight", "prevHeight", "newHeight", "Math", "min", "handleDecreaseHeight", "max", "triggerImageUpload", "_document$getElementB", "document", "getElementById", "click", "currentContainerColor", "find", "item", "style", "handleDeleteSection", "handleLinkSubmit", "key", "handleCloneImgContainer", "handleCloseColorPicker", "handleColorChange", "color", "hex", "handleBackgroundColorClick", "POSITIONING_CONSTANTS", "IMAGE_POPOVER", "HEIGHT", "WIDTH", "REQUIRED_GAP", "FALLBACK_GAPS", "SETTINGS_POPOVER", "GAP", "VIEWPORT", "MIN_TOP_MARGIN", "MIN_SIDE_MARGIN", "Z_INDEX", "getDOMElements", "dialog", "querySelector", "guidePopup", "perfectScrollbar", "getGuidePopupPosition", "element", "rect", "getBoundingClientRect", "top", "left", "calculateGapPosition", "guideTop", "popoverHeight", "requiredGap", "minTopMargin", "positionWithGap", "availableSpace", "constrainToViewport", "position", "size", "viewportSize", "getImagePopoverPosition", "guidePopupPos", "popoverWidth", "innerHeight", "viewportHeight", "innerWidth", "viewportWidth", "window", "topPosition", "centerOffset", "leftPosition", "zIndex", "getSettingsPopoverPosition", "settingsHeight", "settings<PERSON><PERSON><PERSON>", "updatePopoverPositions", "createDebouncedHandler", "handler", "delay", "timeoutId", "clearTimeout", "setTimeout", "debouncedUpdate", "positionCheckInterval", "setInterval", "clearInterval", "qadptPositionCheckInterval", "addEventListener", "resizeObserver", "mutationObserver", "ResizeObserver", "SIGNIFICANT_CHANGE_THRESHOLD", "entries", "hasSignificantChange", "entry", "contentRect", "lastWidth", "parseFloat", "dataset", "lastHeight", "abs", "toString", "for<PERSON>ach", "observe", "MutationObserver", "LAYOUT_AFFECTING_ATTRIBUTES", "mutations", "shouldUpdate", "some", "mutation", "type", "attributeName", "childList", "subtree", "attributes", "attributeFilter", "_resizeObserver", "_mutationObserver", "removeEventListener", "intervalId", "disconnect", "children", "map", "_item$images$", "_item$images$2", "_item$images$3", "_item$style", "imageSrc", "images", "currentSecHeight", "sx", "onClick", "e", "component", "onMouseOver", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textAlign", "dangerouslySetInnerHTML", "__html", "variant", "align", "fontSize", "fontWeight", "onChange", "onKeyDown", "autoFocus", "title", "pointerEvents", "cursor", "opacity", "className", "_document$getElementB2", "stopPropagation", "accept", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "anchorEl", "anchorReference", "slotProps", "paper", "marginLeft", "marginRight", "disabled", "p", "mt", "marginBottom", "select", "fullWidth", "borderColor", "currentC<PERSON><PERSON>", "c", "currentImage", "img", "currentObjectFit", "isSelected", "border", "backgroundBlendMode", "handleModelClose", "onImageSelect", "transform<PERSON><PERSON>in", "_c", "$RefreshReg$"], "sources": ["E:/Code/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/Imagesection.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport {\r\n\t<PERSON>,\r\n\tTypo<PERSON>,\r\n\tPopover,\r\n\tIconButton,\r\n\tTextField,\r\n\tMenuItem,\r\n\tButton,\r\n\tTooltip,\r\n\tSnackbar,\r\n\tAlert\r\n} from \"@mui/material\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport { FileUpload } from \"../../../models/FileUpload\";\r\n\r\nimport {\r\n\tuploadfile,\r\n\thyperlink,\r\n\tfiles,\r\n\tuploadicon,\r\n\treplaceimageicon,\r\n\tcopyicon,\r\n\tdeleteicon,\r\n\tsectionheight,\r\n\tSettings,\r\n\tCrossIcon,\r\n} from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, {\r\n\tIMG_CONTAINER_DEFAULT_HEIGHT,\r\n\tIMG_CONTAINER_MAX_HEIGHT,\r\n\tIMG_CONTAINER_MIN_HEIGHT,\r\n\tIMG_OBJECT_FIT,\r\n\tIMG_STEP_VALUE,\r\n} from \"../../../store/drawerStore\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport \"./PopupSections.css\";\r\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\r\n\r\nconst ImageSection = ({ setImageSrc, setImageName, onDelete, onClone, isCloneDisabled }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tuploadImage,\r\n\t\timagesContainer,\r\n\t\timageAnchorEl,\r\n\t\tsetImageAnchorEl,\r\n\t\treplaceImage,\r\n\t\tcloneImageContainer,\r\n\t\tdeleteImageContainer,\r\n\t\tupdateImageContainer,\r\n\t\ttoggleFit,\r\n\t\tsetImageSrc: storeImageSrc,\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\tconst [snackbarMessage, setSnackbarMessage] = useState('');\r\n\tconst [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('info');\r\n\r\n\tconst [snackbarKey, setSnackbarKey] = useState<number>(0); \r\n\r\n\tconst openSnackbar = () => {\r\n\t\tsetSnackbarKey(prev => prev + 1);\r\n\t\tsetSnackbarOpen(true);\r\n\t};\r\n\tconst closeSnackbar = () => {\r\n\t\tsetSnackbarOpen(false);\r\n\t};\r\n\tconst [showHyperlinkInput, setShowHyperlinkInput] = useState<{ currentContainerId: string; isOpen: boolean }>({\r\n\t\tcurrentContainerId: \"\",\r\n\t\tisOpen: false,\r\n\t});\r\n\tconst [imageLink, setImageLink] = useState<string>(\"\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [currentImageSectionInfo, setCurrentImageSectionInfo] = useState<{\r\n\t\tcurrentContainerId: string;\r\n\t\tisImage: boolean;\r\n\t\theight: number;\r\n\t}>({ currentContainerId: \"\", isImage: false, height: IMG_CONTAINER_DEFAULT_HEIGHT });\r\n\r\n\tconst [selectedAction, setSelectedAction] = useState(\"none\");\r\n\tconst [isModelOpen, setModelOpen] = useState(false);\r\n\tconst [formOfUpload, setFormOfUpload] = useState<String>(\"\");\r\n\tconst [settingsAnchorEl, setSettingsAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [selectedColor, setSelectedColor] = useState<string>(\"#313030\");\r\n\tconst [isReplaceImage, setReplaceImage] = useState(false);\r\n\tconst guidePopupRef = useRef<HTMLElement | null>(null);\r\n\tconst [popoverPositions, setPopoverPositions] = useState({\r\n\t\timagePopover: {},\r\n\t\tsettingsPopover: {}\r\n\t});\r\n\r\n\r\n\tconst openSettingsPopover = Boolean(settingsAnchorEl);\r\n\r\n\tconst handleActionChange = (event: any) => {\r\n\t\tsetSelectedAction(event.target.value);\r\n\t};\r\n\r\n\tconst handleSettingsClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSettingsAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleCloseSettingsPopover = () => {\r\n\t\tsetSettingsAnchorEl(null);\r\n\t};\r\n\tconst imageContainerStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\talignItems: \"center\",\r\n\t\tpadding: 0,\r\n\t\tmargin: 0,\r\n\t\toverflow: \"hidden\",\r\n\t};\r\n\r\n\tconst imageStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tmargin: 0,\r\n\t\tpadding: 0,\r\n\t\tborderRadius: \"0\",\r\n\t};\r\n\r\n\tconst iconRowStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tgap: \"16px\",\r\n\t\tmarginTop: \"10px\",\r\n\t};\r\n\r\n\tconst iconTextStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\twidth: \"100%\",\r\n\t};\r\n\r\n\tconst handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst parts = file.name.split('.');\r\n   \t\t\tconst extension = parts.pop();\r\n\r\n   \t\t // Check for double extensions (e.g. file.html.png) or missing/invalid extension\r\n   \t\t\t if (parts.length > 1 || !extension ) {\r\n\t\t\t  setSnackbarMessage(\"Uploaded file name should not contain any special character\");\r\n       \t\t setSnackbarSeverity(\"error\");\r\n\t\t\t setSnackbarOpen(true);\r\n\t\t\t event.target.value = '';\r\n      \t\t return;\r\n\t\t\t \r\n   \t\t\t }\r\n\t\t\t if(file.name.length > 128){\r\n\t\t\t\tsetSnackbarMessage(\"File name should not exceed 128 characters\");\r\n       \t\t\tsetSnackbarSeverity(\"error\");\r\n\t\t\t \tsetSnackbarOpen(true);\r\n\t\t\t \tevent.target.value = '';\r\n      \t\t \treturn;\r\n\t\t\t }\r\n\t\t\tsetImageName(event.target.files?.[0].name);\r\n\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\tconst base64Image = reader.result as string;\r\n\t\t\t\tstoreImageSrc(base64Image);\r\n\t\t\t\tsetImageSrc(base64Image);\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\turl: base64Image,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\r\n\tconst handleImageUploadFormApp = (file: FileUpload) => {\r\n\t\tif (file) {\r\n\t\t\tstoreImageSrc(file.Url);\r\n\t\t\tsetImageSrc(file.Url);\r\n\t\t\tif (isReplaceImage) {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: file.Url,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t\tsetReplaceImage(false);\r\n\t\t\t} else {\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: crypto.randomUUID(), // Use existing ID\r\n\t\t\t\t\turl: file.Url, // Directly use the URL\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\tconst handleReplaceImage = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: reader.result,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClick = (\r\n\t\tevent: React.MouseEvent<HTMLElement>,\r\n\t\tcontainerId: string,\r\n\t\timageId: string,\r\n\t\tisImage: boolean,\r\n\t\tcurrentHeight: number\r\n\t) => {\r\n\t\t// @ts-ignore\r\n\t\tif ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: imageId,\r\n\t\t\tcontainerId: containerId,\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: event.currentTarget,\r\n\t\t});\r\n\t\tsetSettingsAnchorEl(null);\r\n\t\tsetCurrentImageSectionInfo({\r\n\t\t\tcurrentContainerId: containerId,\r\n\t\t\tisImage,\r\n\t\t\theight: currentHeight,\r\n\t\t});\r\n\t\tsetShowHyperlinkInput({\r\n\t\t\tcurrentContainerId: \"\",\r\n\t\t\tisOpen: false,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t};\r\n\r\n\tconst open = Boolean(imageAnchorEl.value);\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\r\n\tconst id = open ? \"image-popover\" : undefined;\r\n\r\n\tconst handleIncreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\r\n\t\tconst newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst handleDecreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\r\n\t\tconst newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst triggerImageUpload = () => {\r\n\t\tdocument.getElementById(\"replace-upload\")?.click();\r\n\t\t// setModelOpen(true);\r\n\t\t// setReplaceImage(true);\r\n\t};\r\n\r\n\tconst currentContainerColor =\r\n\t\timagesContainer.find((item) => item.id === imageAnchorEl.containerId)?.style.backgroundColor || \"transparent\";\r\n\t// Function to delete the section\r\n\tconst handleDeleteSection = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\r\n\t\t// Call the delete function from the store\r\n\t\tdeleteImageContainer(imageAnchorEl.containerId);\r\n\r\n\t\t// Call the onDelete callback if provided\r\n\t\tif (onDelete) {\r\n\t\t\tonDelete();\r\n\t\t}\r\n\t};\r\n\r\n\r\n\tconst handleLinkSubmit = (event: React.KeyboardEvent<HTMLInputElement>) => {\r\n\t\tif (event.key === \"Enter\" && imageLink) {\r\n\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\taltText: \"New Image\",\r\n\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\turl: imageLink,\r\n\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t});\r\n\t\t\tsetShowHyperlinkInput({\r\n\t\t\t\tcurrentContainerId: \"\",\r\n\t\t\t\tisOpen: false,\r\n\t\t\t});\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloneImgContainer = () => {\r\n\t\t// Check if cloning is disabled due to section limits\r\n\t\tif (isCloneDisabled) {\r\n\t\t\treturn; // Don't clone if limit is reached\r\n\t\t}\r\n\r\n\t\t// Call the clone function from the store\r\n\t\tcloneImageContainer(imageAnchorEl.containerId);\r\n\r\n\t\t// Call the onClone callback if provided\r\n\t\tif (onClone) {\r\n\t\t\tonClone();\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\tsetSelectedColor(color.hex);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\t// Constants for positioning calculations\r\n\tconst POSITIONING_CONSTANTS = {\r\n\t\tIMAGE_POPOVER: {\r\n\t\t\tHEIGHT: 40,\r\n\t\t\tWIDTH: 500,\r\n\t\t\tREQUIRED_GAP: 10,\r\n\t\t\tFALLBACK_GAPS: [10, 5, 2]\r\n\t\t},\r\n\t\tSETTINGS_POPOVER: {\r\n\t\t\tHEIGHT: 200,\r\n\t\t\tWIDTH: 300,\r\n\t\t\tGAP: 10\r\n\t\t},\r\n\t\tVIEWPORT: {\r\n\t\t\tMIN_TOP_MARGIN: 8,\r\n\t\t\tMIN_SIDE_MARGIN: 10\r\n\t\t},\r\n\t\tZ_INDEX: 999999\r\n\t};\r\n\r\n\t// Cached DOM selectors for performance\r\n\tconst getDOMElements = () => ({\r\n\t\tdialog: document.querySelector('.qadpt-guide-popup .MuiDialog-paper'),\r\n\t\tguidePopup: document.getElementById('guide-popup'),\r\n\t\tperfectScrollbar: document.querySelector('.qadpt-guide-popup .ps')\r\n\t});\r\n\r\n\t// Function to get GuidePopUp Dialog container position for consistent positioning\r\n\tconst getGuidePopupPosition = () => {\r\n\t\tconst { dialog, guidePopup } = getDOMElements();\r\n\r\n\t\t// Always use the Dialog container for consistent positioning\r\n\t\tconst element = dialog || guidePopup;\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top,\r\n\t\t\t\tleft: rect.left,\r\n\t\t\t\twidth: rect.width,\r\n\t\t\t\theight: rect.height\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\r\n\t// Helper function to calculate gap-based positioning\r\n\tconst calculateGapPosition = (guideTop: number, popoverHeight: number, requiredGap: number, minTopMargin: number) => {\r\n\t\tconst positionWithGap = guideTop - popoverHeight - requiredGap;\r\n\r\n\t\tif (positionWithGap >= minTopMargin) {\r\n\t\t\treturn positionWithGap;\r\n\t\t}\r\n\r\n\t\t// Try fallback gaps in order of preference\r\n\t\tfor (const gap of POSITIONING_CONSTANTS.IMAGE_POPOVER.FALLBACK_GAPS) {\r\n\t\t\tconst availableSpace = guideTop - popoverHeight - minTopMargin;\r\n\t\t\tif (availableSpace >= gap) {\r\n\t\t\t\treturn guideTop - popoverHeight - gap;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Extreme constraint fallback\r\n\t\treturn Math.max(minTopMargin, guideTop - popoverHeight - POSITIONING_CONSTANTS.IMAGE_POPOVER.FALLBACK_GAPS[2]);\r\n\t};\r\n\r\n\t// Helper function to constrain position within viewport bounds\r\n\tconst constrainToViewport = (position: number, size: number, viewportSize: number, margin: number) => {\r\n\t\treturn Math.max(margin, Math.min(position, viewportSize - size - margin));\r\n\t};\r\n\r\n\t// Enhanced positioning for image properties popover - handles 3+ sections with scrolling\r\n\tconst getImagePopoverPosition = () => {\r\n\t\tconst guidePopupPos = getGuidePopupPosition();\r\n\t\tif (!guidePopupPos) return {};\r\n\r\n\t\tconst { HEIGHT: popoverHeight, WIDTH: popoverWidth, REQUIRED_GAP } = POSITIONING_CONSTANTS.IMAGE_POPOVER;\r\n\t\tconst { MIN_TOP_MARGIN, MIN_SIDE_MARGIN } = POSITIONING_CONSTANTS.VIEWPORT;\r\n\t\tconst { innerHeight: viewportHeight, innerWidth: viewportWidth } = window;\r\n\r\n\t\t// Calculate top position with gap logic\r\n\t\tlet topPosition = calculateGapPosition(guidePopupPos.top, popoverHeight, REQUIRED_GAP, MIN_TOP_MARGIN);\r\n\r\n\t\t// Final safety check for viewport bottom\r\n\t\ttopPosition = Math.min(topPosition, viewportHeight - popoverHeight);\r\n\r\n\t\t// Calculate horizontal position - center relative to GuidePopUp\r\n\t\tconst centerOffset = popoverWidth / 2;\r\n\t\tlet leftPosition = guidePopupPos.left + (guidePopupPos.width / 2) - centerOffset;\r\n\r\n\t\t// Constrain to viewport bounds\r\n\t\tleftPosition = constrainToViewport(leftPosition, popoverWidth, viewportWidth, MIN_SIDE_MARGIN);\r\n\r\n\t\treturn {\r\n\t\t\ttop: topPosition,\r\n\t\t\tleft: leftPosition,\r\n\t\t\tposition: 'fixed' as const,\r\n\t\t\tzIndex: POSITIONING_CONSTANTS.Z_INDEX\r\n\t\t};\r\n\t};\r\n\r\n\t// Enhanced positioning for settings popup - maintains consistent relative positioning\r\n\tconst getSettingsPopoverPosition = () => {\r\n\t\tconst guidePopupPos = getGuidePopupPosition();\r\n\t\tif (!guidePopupPos) return {};\r\n\r\n\t\tconst { HEIGHT: settingsHeight, WIDTH: settingsWidth, GAP } = POSITIONING_CONSTANTS.SETTINGS_POPOVER;\r\n\t\tconst { MIN_SIDE_MARGIN } = POSITIONING_CONSTANTS.VIEWPORT;\r\n\t\tconst { innerHeight: viewportHeight, innerWidth: viewportWidth } = window;\r\n\r\n\t\t// Calculate position to the right of GuidePopUp with consistent spacing\r\n\t\tlet leftPosition = guidePopupPos.left + guidePopupPos.width + GAP;\r\n\t\tlet topPosition = guidePopupPos.top + (guidePopupPos.height / 2) - (settingsHeight / 2) + GAP;\r\n\r\n\t\t// Handle viewport constraints - if not enough space to the right, position to the left\r\n\t\tif (leftPosition + settingsWidth > viewportWidth - MIN_SIDE_MARGIN) {\r\n\t\t\tleftPosition = guidePopupPos.left - settingsWidth - GAP;\r\n\t\t}\r\n\r\n\t\t// Constrain to viewport bounds\r\n\t\ttopPosition = constrainToViewport(topPosition, settingsHeight, viewportHeight, MIN_SIDE_MARGIN);\r\n\t\tleftPosition = constrainToViewport(leftPosition, settingsWidth, viewportWidth, MIN_SIDE_MARGIN);\r\n\r\n\t\treturn {\r\n\t\t\ttop: topPosition,\r\n\t\t\tleft: leftPosition,\r\n\t\t\tposition: 'fixed' as const,\r\n\t\t\tzIndex: POSITIONING_CONSTANTS.Z_INDEX\r\n\t\t};\r\n\t};\r\n\r\n\t// Update popover positions\r\n\tconst updatePopoverPositions = () => {\r\n\t\tsetPopoverPositions({\r\n\t\t\timagePopover: getImagePopoverPosition(),\r\n\t\t\tsettingsPopover: getSettingsPopoverPosition()\r\n\t\t});\r\n\t};\r\n\r\n\t// Optimized event handlers with debouncing\r\n\tconst createDebouncedHandler = (handler: () => void, delay: number = 16) => {\r\n\t\tlet timeoutId: NodeJS.Timeout;\r\n\t\treturn () => {\r\n\t\t\tclearTimeout(timeoutId);\r\n\t\t\ttimeoutId = setTimeout(handler, delay);\r\n\t\t};\r\n\t};\r\n\r\n\t// Effect to handle responsive positioning\r\n\tuseEffect(() => {\r\n\t\tconst debouncedUpdate = createDebouncedHandler(updatePopoverPositions);\r\n\r\n\t\t// Update positions when popovers are opened\r\n\t\tif (open || openSettingsPopover) {\r\n\t\t\tupdatePopoverPositions();\r\n\r\n\t\t\t// Set up periodic position verification with optimized interval\r\n\t\t\tconst positionCheckInterval = setInterval(() => {\r\n\t\t\t\tif (open || openSettingsPopover) {\r\n\t\t\t\t\tupdatePopoverPositions();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tclearInterval(positionCheckInterval);\r\n\t\t\t\t}\r\n\t\t\t}, 200);\r\n\r\n\t\t\t// Store interval ID for cleanup\r\n\t\t\t(window as any).qadptPositionCheckInterval = positionCheckInterval;\r\n\t\t}\r\n\r\n\t\t// Get DOM elements once for efficiency\r\n\t\tconst { dialog, guidePopup, perfectScrollbar } = getDOMElements();\r\n\r\n\t\t// Add event listeners for window resize and scroll\r\n\t\twindow.addEventListener('resize', debouncedUpdate);\r\n\t\twindow.addEventListener('scroll', debouncedUpdate);\r\n\r\n\t\t// Add scroll listener for PerfectScrollbar within GuidePopUp\r\n\t\tif (perfectScrollbar) {\r\n\t\t\tperfectScrollbar.addEventListener('ps-scroll-y', debouncedUpdate);\r\n\t\t\tperfectScrollbar.addEventListener('scroll', debouncedUpdate);\r\n\t\t}\r\n\r\n\t\t// Optimized observers setup\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\t// Enhanced ResizeObserver with change detection\r\n\t\tif (window.ResizeObserver) {\r\n\t\t\tconst SIGNIFICANT_CHANGE_THRESHOLD = 1;\r\n\r\n\t\t\tresizeObserver = new ResizeObserver((entries) => {\r\n\t\t\t\tlet hasSignificantChange = false;\r\n\r\n\t\t\t\tfor (const entry of entries) {\r\n\t\t\t\t\tconst { width, height } = entry.contentRect;\r\n\t\t\t\t\tconst element = entry.target as HTMLElement;\r\n\t\t\t\t\tconst lastWidth = parseFloat(element.dataset.lastWidth || '0');\r\n\t\t\t\t\tconst lastHeight = parseFloat(element.dataset.lastHeight || '0');\r\n\r\n\t\t\t\t\tif (Math.abs(width - lastWidth) > SIGNIFICANT_CHANGE_THRESHOLD ||\r\n\t\t\t\t\t\tMath.abs(height - lastHeight) > SIGNIFICANT_CHANGE_THRESHOLD) {\r\n\t\t\t\t\t\thasSignificantChange = true;\r\n\t\t\t\t\t\telement.dataset.lastWidth = width.toString();\r\n\t\t\t\t\t\telement.dataset.lastHeight = height.toString();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (hasSignificantChange) {\r\n\t\t\t\t\tupdatePopoverPositions();\r\n\t\t\t\t\t// Debounced follow-up for stability\r\n\t\t\t\t\tsetTimeout(updatePopoverPositions, 50);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\t// Observe elements if they exist\r\n\t\t\t[guidePopup, dialog, perfectScrollbar].forEach(element => {\r\n\t\t\t\tif (element) resizeObserver!.observe(element);\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t// Enhanced MutationObserver with targeted observation\r\n\t\tif (guidePopup && window.MutationObserver) {\r\n\t\t\tconst LAYOUT_AFFECTING_ATTRIBUTES = ['style', 'class'];\r\n\r\n\t\t\tmutationObserver = new MutationObserver((mutations) => {\r\n\t\t\t\tconst shouldUpdate = mutations.some(mutation =>\r\n\t\t\t\t\tmutation.type === 'childList' ||\r\n\t\t\t\t\t(mutation.type === 'attributes' &&\r\n\t\t\t\t\t LAYOUT_AFFECTING_ATTRIBUTES.includes(mutation.attributeName || ''))\r\n\t\t\t\t);\r\n\r\n\t\t\t\tif (shouldUpdate) {\r\n\t\t\t\t\tsetTimeout(updatePopoverPositions, 50);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\tmutationObserver.observe(guidePopup, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: LAYOUT_AFFECTING_ATTRIBUTES\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t// Cleanup function\r\n\t\treturn () => {\r\n\t\t\twindow.removeEventListener('resize', debouncedUpdate);\r\n\t\t\twindow.removeEventListener('scroll', debouncedUpdate);\r\n\r\n\t\t\t// Remove PerfectScrollbar event listeners\r\n\t\t\tif (perfectScrollbar) {\r\n\t\t\t\tperfectScrollbar.removeEventListener('ps-scroll-y', debouncedUpdate);\r\n\t\t\t\tperfectScrollbar.removeEventListener('scroll', debouncedUpdate);\r\n\t\t\t}\r\n\r\n\t\t\t// Cleanup periodic position check\r\n\t\t\tconst intervalId = (window as any).qadptPositionCheckInterval;\r\n\t\t\tif (intervalId) {\r\n\t\t\t\tclearInterval(intervalId);\r\n\t\t\t\tdelete (window as any).qadptPositionCheckInterval;\r\n\t\t\t}\r\n\r\n\t\t\t// Disconnect observers\r\n\t\t\tresizeObserver?.disconnect();\r\n\t\t\tmutationObserver?.disconnect();\r\n\t\t};\r\n\t}, [open, openSettingsPopover]);\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{imagesContainer.map((item) => {\r\n\t\t\t\tconst imageSrc = item.images[0]?.url;\r\n\t\t\t\tconst imageId = item.images[0]?.id;\r\n\t\t\t\tconst objectFit = item.images[0]?.objectFit || IMG_OBJECT_FIT;\r\n\t\t\t\tconst currentSecHeight = (item?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT;\r\n\t\t\t\tconst id = item.id;\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tkey={id}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t// padding: \"5px\",\r\n\t\t\t\t\t\t\tmargin: \"0px\",\r\n\t\t\t\t\t\t\toverflow: \"auto\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t...imageContainerStyle,\r\n\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor,\r\n\t\t\t\t\t\t\t\theight: `${item.style.height}px`,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={(e) => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight)}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid={id}\r\n\t\t\t\t\t\t\tonMouseOver={() => {\r\n\t\t\t\t\t\t\t\tsetImageAnchorEl({\r\n\t\t\t\t\t\t\t\t\tbuttonId: imageId,\r\n\t\t\t\t\t\t\t\t\tcontainerId: id,\r\n\t\t\t\t\t\t\t\t\tvalue: null,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{imageSrc ? (\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={imageSrc}\r\n\t\t\t\t\t\t\t\t\talt=\"Uploaded\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ ...imageStyle, objectFit }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tsx={iconTextStyle}\r\n\t\t\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadfile }}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"inline-block\" }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"h6\"\r\n\t\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\", fontWeight: \"600\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Upload file\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Drag & Drop to upload file\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ marginTop: \"8px\", fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Or\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t{showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? (\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={imageLink}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setImageLink(e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tonKeyDown={handleLinkSubmit}\r\n\t\t\t\t\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={iconRowStyle}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n  <div style={{ pointerEvents: \"auto\", cursor:\"pointer\"}}>\r\n    <span\r\n      dangerouslySetInnerHTML={{ __html: hyperlink }}\r\n      style={{\r\n        color: \"black\",\r\n        cursor: \"pointer\",\r\n        fontSize: \"32px\",\r\n        opacity: \"0.5\",\r\n        pointerEvents: \"none\",\r\n      }}\r\n      id=\"hyperlink\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n    />\r\n  </div>\r\n</Tooltip>\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t//setModelOpen(true);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: files }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\", opacity: \"0.5\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"folder\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t//title=\"Coming Soon\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Upload File\")}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tevent?.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdocument.getElementById(\"file-upload\")?.click();\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload1\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleImageUpload}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={closeSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Alert onClose={closeSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{snackbarMessage}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Alert>\r\n\t\t\t\t\t\t\t\t\t\t\t</Snackbar>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t);\r\n\t\t\t})}\r\n\t\t\t{Boolean(imageAnchorEl.value) ? (\r\n\t\t\t\t<Popover\r\n\t\t\t\t\tclassName=\"qadpt-imgsec-popover\"\r\n\t\t\t\t\tid={id}\r\n\t\t\t\t\topen={open}\r\n\t\t\t\t\tanchorEl={null}\r\n\t\t\t\t\tonClose={handleClose}\r\n\t\t\t\t\tanchorReference=\"none\"\r\n\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\t\t...popoverPositions.imagePopover,\r\n\t\t\t\t\t\t\t\theight: 'auto',\r\n\t\t\t\t\t\t\t\twidth: 'auto',\r\n\t\t\t\t\t\t\t\tpadding: '5px 10px',\r\n\t\t\t\t\t\t\t\tmarginLeft: 'auto',\r\n\t\t\t\t\t\t\t\tmarginRight: 'auto',\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tgap: \"15px\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tpadding: \"0 10px\",\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box sx={{ display: \"flex\" }}>\r\n\t\t\t\t\t\t\t{currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId &&\r\n\t\t\t\t\t\t\tcurrentImageSectionInfo.isImage ? (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tfontSize=\"12px\"\r\n\t\t\t\t\t\t\t\t\t\tmarginLeft={\"5px\"}\r\n\t\t\t\t\t\t\t\t\t\tonClick={triggerImageUpload}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Replace Image\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\tid=\"replace-upload\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleReplaceImage}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t</Box>\r\n\r\n<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-tool-items\"\r\n\t\t\t\t\t\t\tsx={{ display: \"flex\", alignItems: \"center\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: sectionheight }}\r\n\t\t\t\t\t\t\tstyle={{ display: \"flex\" }}/>\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\") : translate(\"Decrease height\")}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleDecreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<RemoveIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t<Typography fontSize=\"12px\">{currentImageSectionInfo.height}</Typography>\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\") : translate(\"Increase height\")}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleIncreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<AddIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Settings\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={handleSettingsClick}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Settings }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\"}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-imgset\"\r\n\t\t\t\t\t\t\t\topen={openSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorEl={null}\r\n\t\t\t\t\t\t\t\tonClose={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorReference=\"none\"\r\n\t\t\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\t\t\t\t\t...popoverPositions.settingsPopover,\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"205px\",\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box p={2}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"subtitle1\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ color: \"rgba(95, 158, 160, 1)\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Properties\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: CrossIcon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{ marginBottom: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Actions\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tselect\r\n\t\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={selectedAction}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleActionChange}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"none\">{translate(\"None\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"specificStep\">{translate(\"Specific Step\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"openUrl\">{translate(\"Open URL\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"clickElement\">{translate(\"Click Element\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startTour\">{translate(\"Start Tour\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startMicroSurvey\">{translate(\"Start Micro Survey\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t</TextField>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Formatting\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\tgap={1}\r\n\t\t\t\t\t\t\t\t\t\t\tmt={1}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{[\"Fill\", \"Fit\"].map((item) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Get current image's objectFit to determine selected state\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentContainer = imagesContainer.find((c) => c.id === imageAnchorEl.containerId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentImage = currentContainer?.images.find((img) => img.id === imageAnchorEl.buttonId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentObjectFit = currentImage?.objectFit || IMG_OBJECT_FIT;\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Determine if this button should be selected\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst isSelected = (item === \"Fill\" && currentObjectFit === \"cover\") ||\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  (item === \"Fit\" && currentObjectFit === \"contain\");\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttoggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item as \"Fit\" | \"Fill\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"88.5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"41px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"10px 12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px 6px 6px 6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"1px solid rgba(95, 158, 160, 1)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"1px solid rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundBlendMode: \"multiply\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(item)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Background Color\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: selectedColor,\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"100%\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t}} />\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\") : translate(\"Clone Section\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleCloneImgContainer}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Delete Section\")}>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleDeleteSection}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Popover>\r\n\t\t\t) : null}\r\n\t\t\t{\r\n\t\t\t\tisModelOpen && (\r\n\t\t\t\t\t<SelectImageFromApplication isOpen={isModelOpen} handleModelClose={() => setModelOpen(false)} onImageSelect={handleImageUploadFormApp} setFormOfUpload={setFormOfUpload} formOfUpload={formOfUpload} handleReplaceImage={handleReplaceImage} isReplaceImage={isReplaceImage}/>\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t\t<Popover\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={currentContainerColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<style>\r\n    {`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n  </style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ImageSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACCC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,KAAK,QACC,eAAe;AACtB,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,SAASC,cAAc,QAAQ,eAAe;AAI9C,SACCC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,QAAQ,EACRC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,SAAS,QACH,6BAA6B;AACpC,OAAOC,cAAc,IACpBC,4BAA4B,EAC5BC,wBAAwB,EACxBC,wBAAwB,EACxBC,cAAc,EACdC,cAAc,QACR,4BAA4B;AACnC,SAASC,YAAY,QAAqB,aAAa;AACvD,OAAO,qBAAqB;AAC5B,OAAOC,0BAA0B,MAAM,yCAAyC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjF,MAAMC,YAAY,GAAGA,CAAC;EAAEC,WAAW;EAAEC,YAAY;EAAEC,QAAQ;EAAEC,OAAO;EAAEC;AAAqB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChG,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGhC,cAAc,CAAC,CAAC;EACzC,MAAM;IACLiC,WAAW;IACXC,eAAe;IACfC,aAAa;IACbC,gBAAgB;IAChBC,YAAY;IACZC,mBAAmB;IACnBC,oBAAoB;IACpBC,oBAAoB;IACpBC,SAAS;IACTjB,WAAW,EAAEkB;EACd,CAAC,GAAG/B,cAAc,CAAEgC,KAAK,IAAKA,KAAK,CAAC;EACpC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC8D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAA2C,MAAM,CAAC;EAE1G,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAS,CAAC,CAAC;EAEzD,MAAMkE,YAAY,GAAGA,CAAA,KAAM;IAC1BD,cAAc,CAACE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAChCR,eAAe,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC3BT,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD,MAAM,CAACU,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtE,QAAQ,CAAkD;IAC7GuE,kBAAkB,EAAE,EAAE;IACtBC,MAAM,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAAC2E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5E,QAAQ,CAAqB,IAAI,CAAC;EACxF,MAAM,CAAC6E,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG9E,QAAQ,CAInE;IAAEuE,kBAAkB,EAAE,EAAE;IAAEQ,OAAO,EAAE,KAAK;IAAEC,MAAM,EAAEtD;EAA6B,CAAC,CAAC;EAEpF,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlF,QAAQ,CAAC,MAAM,CAAC;EAC5D,MAAM,CAACmF,WAAW,EAAEC,YAAY,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqF,YAAY,EAAEC,eAAe,CAAC,GAAGtF,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACuF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxF,QAAQ,CAAqB,IAAI,CAAC;EAClF,MAAM,CAACyF,aAAa,EAAEC,gBAAgB,CAAC,GAAG1F,QAAQ,CAAS,SAAS,CAAC;EACrE,MAAM,CAAC2F,cAAc,EAAEC,eAAe,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM6F,aAAa,GAAG5F,MAAM,CAAqB,IAAI,CAAC;EACtD,MAAM,CAAC6F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/F,QAAQ,CAAC;IACxDgG,YAAY,EAAE,CAAC,CAAC;IAChBC,eAAe,EAAE,CAAC;EACnB,CAAC,CAAC;EAGF,MAAMC,mBAAmB,GAAGC,OAAO,CAACZ,gBAAgB,CAAC;EAErD,MAAMa,kBAAkB,GAAIC,KAAU,IAAK;IAC1CnB,iBAAiB,CAACmB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACtC,CAAC;EAED,MAAMC,mBAAmB,GAAIH,KAAoC,IAAK;IACrEb,mBAAmB,CAACa,KAAK,CAACI,aAAa,CAAC;EACzC,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACxClB,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EACD,MAAMmB,mBAAwC,GAAG;IAChDC,KAAK,EAAE,MAAM;IACb5B,MAAM,EAAE,MAAM;IACd6B,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACX,CAAC;EAED,MAAMC,UAA+B,GAAG;IACvCP,KAAK,EAAE,MAAM;IACb5B,MAAM,EAAE,MAAM;IACdiC,MAAM,EAAE,CAAC;IACTD,OAAO,EAAE,CAAC;IACVI,YAAY,EAAE;EACf,CAAC;EAED,MAAMC,YAAiC,GAAG;IACzCR,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBQ,GAAG,EAAE,MAAM;IACXC,SAAS,EAAE;EACZ,CAAC;EAED,MAAMC,aAAkC,GAAG;IAC1CX,OAAO,EAAE,MAAM;IACfY,aAAa,EAAE,QAAQ;IACvBV,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBF,KAAK,EAAE;EACR,CAAC;EAED,MAAMc,iBAAiB,GAAIrB,KAA0C,IAAK;IAAA,IAAAsB,mBAAA;IACzE,MAAMC,IAAI,IAAAD,mBAAA,GAAGtB,KAAK,CAACC,MAAM,CAACrF,KAAK,cAAA0G,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MAAA,IAAAC,oBAAA;MACT,MAAMC,KAAK,GAAGF,IAAI,CAACG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;MAC/B,MAAMC,SAAS,GAAGH,KAAK,CAACI,GAAG,CAAC,CAAC;;MAE7B;MACC,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,IAAI,CAACF,SAAS,EAAG;QACvCpE,kBAAkB,CAAC,6DAA6D,CAAC;QAC5EE,mBAAmB,CAAC,OAAO,CAAC;QAClCJ,eAAe,CAAC,IAAI,CAAC;QACrB0C,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MAEF;MACH,IAAGqB,IAAI,CAACG,IAAI,CAACI,MAAM,GAAG,GAAG,EAAC;QAC1BtE,kBAAkB,CAAC,4CAA4C,CAAC;QAC1DE,mBAAmB,CAAC,OAAO,CAAC;QACjCJ,eAAe,CAAC,IAAI,CAAC;QACrB0C,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MACN;MACDhE,YAAY,EAAAsF,oBAAA,GAACxB,KAAK,CAACC,MAAM,CAACrF,KAAK,cAAA4G,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC,CAACE,IAAI,CAAC;MAE1C,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxB,MAAMC,WAAW,GAAGH,MAAM,CAACI,MAAgB;QAC3ChF,aAAa,CAAC+E,WAAW,CAAC;QAC1BjG,WAAW,CAACiG,WAAW,CAAC;QACxBxF,WAAW,CAACE,aAAa,CAACwF,WAAW,EAAE;UACtCC,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBY,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;UACvBC,GAAG,EAAEP,WAAW;UAChBQ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEnH;QACZ,CAAC,CAAC;MACH,CAAC;MACDuG,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC;IAC3B;IACAxC,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAM8D,wBAAwB,GAAItB,IAAgB,IAAK;IACtD,IAAIA,IAAI,EAAE;MACTpE,aAAa,CAACoE,IAAI,CAACuB,GAAG,CAAC;MACvB7G,WAAW,CAACsF,IAAI,CAACuB,GAAG,CAAC;MACrB,IAAIxD,cAAc,EAAE;QACnBxC,YAAY,CAACF,aAAa,CAACwF,WAAW,EAAExF,aAAa,CAACmG,QAAQ,EAAE;UAC/DV,OAAO,EAAEd,IAAI,CAACyB,QAAQ;UACtBV,EAAE,EAAE1F,aAAa,CAACmG,QAAQ;UAC1BN,GAAG,EAAElB,IAAI,CAACuB,GAAG;UACbJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEnH;QACZ,CAAC,CAAC;QACF+D,eAAe,CAAC,KAAK,CAAC;MACvB,CAAC,MAAM;QACN7C,WAAW,CAACE,aAAa,CAACwF,WAAW,EAAE;UACtCC,OAAO,EAAEd,IAAI,CAACyB,QAAQ;UACtBV,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;UAAE;UACzBC,GAAG,EAAElB,IAAI,CAACuB,GAAG;UAAE;UACfJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEnH;QACZ,CAAC,CAAC;MACH;IACD;IACAuD,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EACD,MAAMkE,kBAAkB,GAAIjD,KAA0C,IAAK;IAAA,IAAAkD,oBAAA;IAC1E,MAAM3B,IAAI,IAAA2B,oBAAA,GAAGlD,KAAK,CAACC,MAAM,CAACrF,KAAK,cAAAsI,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC;IACpC,IAAI3B,IAAI,EAAE;MACT,MAAMQ,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxBnF,YAAY,CAACF,aAAa,CAACwF,WAAW,EAAExF,aAAa,CAACmG,QAAQ,EAAE;UAC/DV,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBY,EAAE,EAAE1F,aAAa,CAACmG,QAAQ;UAC1BN,GAAG,EAAEV,MAAM,CAACI,MAAM;UAClBO,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEnH;QACZ,CAAC,CAAC;MACH,CAAC;MACDuG,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAM4B,WAAW,GAAGA,CACnBnD,KAAoC,EACpCoC,WAAmB,EACnBgB,OAAe,EACf1E,OAAgB,EAChB2E,aAAqB,KACjB;IACJ;IACA,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACtD,KAAK,CAACC,MAAM,CAACqC,EAAE,CAAC,EAAE;IAC5DzF,gBAAgB,CAAC;MAChBkG,QAAQ,EAAEK,OAAO;MACjBhB,WAAW,EAAEA,WAAW;MACxB;MACAlC,KAAK,EAAEF,KAAK,CAACI;IACd,CAAC,CAAC;IACFjB,mBAAmB,CAAC,IAAI,CAAC;IACzBV,0BAA0B,CAAC;MAC1BP,kBAAkB,EAAEkE,WAAW;MAC/B1D,OAAO;MACPC,MAAM,EAAE0E;IACT,CAAC,CAAC;IACFpF,qBAAqB,CAAC;MACrBC,kBAAkB,EAAE,EAAE;MACtBC,MAAM,EAAE;IACT,CAAC,CAAC;EACH,CAAC;EAED,MAAMoF,WAAW,GAAGA,CAAA,KAAM;IACzB1G,gBAAgB,CAAC;MAChBkG,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,EAAE;MACf;MACAlC,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC;EAED,MAAMsD,IAAI,GAAG1D,OAAO,CAAClD,aAAa,CAACsD,KAAK,CAAC;EACzC,MAAMuD,eAAe,GAAG3D,OAAO,CAACxB,mBAAmB,CAAC;EAEpD,MAAMgE,EAAE,GAAGkB,IAAI,GAAG,eAAe,GAAGE,SAAS;EAE7C,MAAMC,oBAAoB,GAAIC,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAItI,wBAAwB,EAAE;IAC5C,MAAMuI,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,GAAGnI,cAAc,EAAEH,wBAAwB,CAAC;IACjF2B,oBAAoB,CAACL,aAAa,CAACwF,WAAW,EAAE,OAAO,EAAE;MACxDzD,MAAM,EAAEkF;IACT,CAAC,CAAC;IACFpF,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAEkF;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,MAAMG,oBAAoB,GAAIJ,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAIrI,wBAAwB,EAAE;IAC5C,MAAMsI,SAAS,GAAGC,IAAI,CAACG,GAAG,CAACL,UAAU,GAAGnI,cAAc,EAAEF,wBAAwB,CAAC;IACjF0B,oBAAoB,CAACL,aAAa,CAACwF,WAAW,EAAE,OAAO,EAAE;MACxDzD,MAAM,EAAEkF;IACT,CAAC,CAAC;IACFpF,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAEkF;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAChC,CAAAA,qBAAA,GAAAC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,cAAAF,qBAAA,uBAAzCA,qBAAA,CAA2CG,KAAK,CAAC,CAAC;IAClD;IACA;EACD,CAAC;EAED,MAAMC,qBAAqB,GAC1B,EAAAhI,qBAAA,GAAAI,eAAe,CAAC6H,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACnC,EAAE,KAAK1F,aAAa,CAACwF,WAAW,CAAC,cAAA7F,qBAAA,uBAArEA,qBAAA,CAAuEmI,KAAK,CAAChC,eAAe,KAAI,aAAa;EAC9G;EACA,MAAMiC,mBAAmB,GAAGA,CAAA,KAAM;IACjC9H,gBAAgB,CAAC;MAChBkG,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,EAAE;MACf;MACAlC,KAAK,EAAE;IACR,CAAC,CAAC;;IAEF;IACAlD,oBAAoB,CAACJ,aAAa,CAACwF,WAAW,CAAC;;IAE/C;IACA,IAAIjG,QAAQ,EAAE;MACbA,QAAQ,CAAC,CAAC;IACX;EACD,CAAC;EAGD,MAAMyI,gBAAgB,GAAI5E,KAA4C,IAAK;IAC1E,IAAIA,KAAK,CAAC6E,GAAG,KAAK,OAAO,IAAIzG,SAAS,EAAE;MACvC1B,WAAW,CAACE,aAAa,CAACwF,WAAW,EAAE;QACtCC,OAAO,EAAE,WAAW;QACpBC,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;QACvBC,GAAG,EAAErE,SAAS;QACdsE,eAAe,EAAE,aAAa;QAC9BC,SAAS,EAAEnH;MACZ,CAAC,CAAC;MACFyC,qBAAqB,CAAC;QACrBC,kBAAkB,EAAE,EAAE;QACtBC,MAAM,EAAE;MACT,CAAC,CAAC;IACH;EACD,CAAC;EAED,MAAM2G,uBAAuB,GAAGA,CAAA,KAAM;IACrC;IACA,IAAIzI,eAAe,EAAE;MACpB,OAAO,CAAC;IACT;;IAEA;IACAU,mBAAmB,CAACH,aAAa,CAACwF,WAAW,CAAC;;IAE9C;IACA,IAAIhG,OAAO,EAAE;MACZA,OAAO,CAAC,CAAC;IACV;EACD,CAAC;EAED,MAAM2I,sBAAsB,GAAGA,CAAA,KAAM;IACpCxG,sBAAsB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMyG,iBAAiB,GAAIC,KAAkB,IAAK;IACjD5F,gBAAgB,CAAC4F,KAAK,CAACC,GAAG,CAAC;IAC3BjI,oBAAoB,CAACL,aAAa,CAACwF,WAAW,EAAE,OAAO,EAAE;MACxDM,eAAe,EAAEuC,KAAK,CAACC;IACxB,CAAC,CAAC;EACH,CAAC;EAED,MAAMC,0BAA0B,GAAInF,KAAoC,IAAK;IAC5EzB,sBAAsB,CAACyB,KAAK,CAACI,aAAa,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMgF,qBAAqB,GAAG;IAC7BC,aAAa,EAAE;MACdC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,GAAG;MACVC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;IACzB,CAAC;IACDC,gBAAgB,EAAE;MACjBJ,MAAM,EAAE,GAAG;MACXC,KAAK,EAAE,GAAG;MACVI,GAAG,EAAE;IACN,CAAC;IACDC,QAAQ,EAAE;MACTC,cAAc,EAAE,CAAC;MACjBC,eAAe,EAAE;IAClB,CAAC;IACDC,OAAO,EAAE;EACV,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,MAAO;IAC7BC,MAAM,EAAE7B,QAAQ,CAAC8B,aAAa,CAAC,qCAAqC,CAAC;IACrEC,UAAU,EAAE/B,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IAClD+B,gBAAgB,EAAEhC,QAAQ,CAAC8B,aAAa,CAAC,wBAAwB;EAClE,CAAC,CAAC;;EAEF;EACA,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;IACnC,MAAM;MAAEJ,MAAM;MAAEE;IAAW,CAAC,GAAGH,cAAc,CAAC,CAAC;;IAE/C;IACA,MAAMM,OAAO,GAAGL,MAAM,IAAIE,UAAU;IACpC,IAAIG,OAAO,EAAE;MACZ,MAAMC,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;MAC5C,OAAO;QACNC,GAAG,EAAEF,IAAI,CAACE,GAAG;QACbC,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfnG,KAAK,EAAEgG,IAAI,CAAChG,KAAK;QACjB5B,MAAM,EAAE4H,IAAI,CAAC5H;MACd,CAAC;IACF;IACA,OAAO,IAAI;EACZ,CAAC;;EAED;EACA,MAAMgI,oBAAoB,GAAGA,CAACC,QAAgB,EAAEC,aAAqB,EAAEC,WAAmB,EAAEC,YAAoB,KAAK;IACpH,MAAMC,eAAe,GAAGJ,QAAQ,GAAGC,aAAa,GAAGC,WAAW;IAE9D,IAAIE,eAAe,IAAID,YAAY,EAAE;MACpC,OAAOC,eAAe;IACvB;;IAEA;IACA,KAAK,MAAM/F,GAAG,IAAImE,qBAAqB,CAACC,aAAa,CAACI,aAAa,EAAE;MACpE,MAAMwB,cAAc,GAAGL,QAAQ,GAAGC,aAAa,GAAGE,YAAY;MAC9D,IAAIE,cAAc,IAAIhG,GAAG,EAAE;QAC1B,OAAO2F,QAAQ,GAAGC,aAAa,GAAG5F,GAAG;MACtC;IACD;;IAEA;IACA,OAAO6C,IAAI,CAACG,GAAG,CAAC8C,YAAY,EAAEH,QAAQ,GAAGC,aAAa,GAAGzB,qBAAqB,CAACC,aAAa,CAACI,aAAa,CAAC,CAAC,CAAC,CAAC;EAC/G,CAAC;;EAED;EACA,MAAMyB,mBAAmB,GAAGA,CAACC,QAAgB,EAAEC,IAAY,EAAEC,YAAoB,EAAEzG,MAAc,KAAK;IACrG,OAAOkD,IAAI,CAACG,GAAG,CAACrD,MAAM,EAAEkD,IAAI,CAACC,GAAG,CAACoD,QAAQ,EAAEE,YAAY,GAAGD,IAAI,GAAGxG,MAAM,CAAC,CAAC;EAC1E,CAAC;;EAED;EACA,MAAM0G,uBAAuB,GAAGA,CAAA,KAAM;IACrC,MAAMC,aAAa,GAAGlB,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACkB,aAAa,EAAE,OAAO,CAAC,CAAC;IAE7B,MAAM;MAAEjC,MAAM,EAAEuB,aAAa;MAAEtB,KAAK,EAAEiC,YAAY;MAAEhC;IAAa,CAAC,GAAGJ,qBAAqB,CAACC,aAAa;IACxG,MAAM;MAAEQ,cAAc;MAAEC;IAAgB,CAAC,GAAGV,qBAAqB,CAACQ,QAAQ;IAC1E,MAAM;MAAE6B,WAAW,EAAEC,cAAc;MAAEC,UAAU,EAAEC;IAAc,CAAC,GAAGC,MAAM;;IAEzE;IACA,IAAIC,WAAW,GAAGnB,oBAAoB,CAACY,aAAa,CAACd,GAAG,EAAEI,aAAa,EAAErB,YAAY,EAAEK,cAAc,CAAC;;IAEtG;IACAiC,WAAW,GAAGhE,IAAI,CAACC,GAAG,CAAC+D,WAAW,EAAEJ,cAAc,GAAGb,aAAa,CAAC;;IAEnE;IACA,MAAMkB,YAAY,GAAGP,YAAY,GAAG,CAAC;IACrC,IAAIQ,YAAY,GAAGT,aAAa,CAACb,IAAI,GAAIa,aAAa,CAAChH,KAAK,GAAG,CAAE,GAAGwH,YAAY;;IAEhF;IACAC,YAAY,GAAGd,mBAAmB,CAACc,YAAY,EAAER,YAAY,EAAEI,aAAa,EAAE9B,eAAe,CAAC;IAE9F,OAAO;MACNW,GAAG,EAAEqB,WAAW;MAChBpB,IAAI,EAAEsB,YAAY;MAClBb,QAAQ,EAAE,OAAgB;MAC1Bc,MAAM,EAAE7C,qBAAqB,CAACW;IAC/B,CAAC;EACF,CAAC;;EAED;EACA,MAAMmC,0BAA0B,GAAGA,CAAA,KAAM;IACxC,MAAMX,aAAa,GAAGlB,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACkB,aAAa,EAAE,OAAO,CAAC,CAAC;IAE7B,MAAM;MAAEjC,MAAM,EAAE6C,cAAc;MAAE5C,KAAK,EAAE6C,aAAa;MAAEzC;IAAI,CAAC,GAAGP,qBAAqB,CAACM,gBAAgB;IACpG,MAAM;MAAEI;IAAgB,CAAC,GAAGV,qBAAqB,CAACQ,QAAQ;IAC1D,MAAM;MAAE6B,WAAW,EAAEC,cAAc;MAAEC,UAAU,EAAEC;IAAc,CAAC,GAAGC,MAAM;;IAEzE;IACA,IAAIG,YAAY,GAAGT,aAAa,CAACb,IAAI,GAAGa,aAAa,CAAChH,KAAK,GAAGoF,GAAG;IACjE,IAAImC,WAAW,GAAGP,aAAa,CAACd,GAAG,GAAIc,aAAa,CAAC5I,MAAM,GAAG,CAAE,GAAIwJ,cAAc,GAAG,CAAE,GAAGxC,GAAG;;IAE7F;IACA,IAAIqC,YAAY,GAAGI,aAAa,GAAGR,aAAa,GAAG9B,eAAe,EAAE;MACnEkC,YAAY,GAAGT,aAAa,CAACb,IAAI,GAAG0B,aAAa,GAAGzC,GAAG;IACxD;;IAEA;IACAmC,WAAW,GAAGZ,mBAAmB,CAACY,WAAW,EAAEK,cAAc,EAAET,cAAc,EAAE5B,eAAe,CAAC;IAC/FkC,YAAY,GAAGd,mBAAmB,CAACc,YAAY,EAAEI,aAAa,EAAER,aAAa,EAAE9B,eAAe,CAAC;IAE/F,OAAO;MACNW,GAAG,EAAEqB,WAAW;MAChBpB,IAAI,EAAEsB,YAAY;MAClBb,QAAQ,EAAE,OAAgB;MAC1Bc,MAAM,EAAE7C,qBAAqB,CAACW;IAC/B,CAAC;EACF,CAAC;;EAED;EACA,MAAMsC,sBAAsB,GAAGA,CAAA,KAAM;IACpC3I,mBAAmB,CAAC;MACnBC,YAAY,EAAE2H,uBAAuB,CAAC,CAAC;MACvC1H,eAAe,EAAEsI,0BAA0B,CAAC;IAC7C,CAAC,CAAC;EACH,CAAC;;EAED;EACA,MAAMI,sBAAsB,GAAGA,CAACC,OAAmB,EAAEC,KAAa,GAAG,EAAE,KAAK;IAC3E,IAAIC,SAAyB;IAC7B,OAAO,MAAM;MACZC,YAAY,CAACD,SAAS,CAAC;MACvBA,SAAS,GAAGE,UAAU,CAACJ,OAAO,EAAEC,KAAK,CAAC;IACvC,CAAC;EACF,CAAC;;EAED;EACA9O,SAAS,CAAC,MAAM;IACf,MAAMkP,eAAe,GAAGN,sBAAsB,CAACD,sBAAsB,CAAC;;IAEtE;IACA,IAAI7E,IAAI,IAAI3D,mBAAmB,EAAE;MAChCwI,sBAAsB,CAAC,CAAC;;MAExB;MACA,MAAMQ,qBAAqB,GAAGC,WAAW,CAAC,MAAM;QAC/C,IAAItF,IAAI,IAAI3D,mBAAmB,EAAE;UAChCwI,sBAAsB,CAAC,CAAC;QACzB,CAAC,MAAM;UACNU,aAAa,CAACF,qBAAqB,CAAC;QACrC;MACD,CAAC,EAAE,GAAG,CAAC;;MAEP;MACChB,MAAM,CAASmB,0BAA0B,GAAGH,qBAAqB;IACnE;;IAEA;IACA,MAAM;MAAE5C,MAAM;MAAEE,UAAU;MAAEC;IAAiB,CAAC,GAAGJ,cAAc,CAAC,CAAC;;IAEjE;IACA6B,MAAM,CAACoB,gBAAgB,CAAC,QAAQ,EAAEL,eAAe,CAAC;IAClDf,MAAM,CAACoB,gBAAgB,CAAC,QAAQ,EAAEL,eAAe,CAAC;;IAElD;IACA,IAAIxC,gBAAgB,EAAE;MACrBA,gBAAgB,CAAC6C,gBAAgB,CAAC,aAAa,EAAEL,eAAe,CAAC;MACjExC,gBAAgB,CAAC6C,gBAAgB,CAAC,QAAQ,EAAEL,eAAe,CAAC;IAC7D;;IAEA;IACA,IAAIM,cAAqC,GAAG,IAAI;IAChD,IAAIC,gBAAyC,GAAG,IAAI;;IAEpD;IACA,IAAItB,MAAM,CAACuB,cAAc,EAAE;MAC1B,MAAMC,4BAA4B,GAAG,CAAC;MAEtCH,cAAc,GAAG,IAAIE,cAAc,CAAEE,OAAO,IAAK;QAChD,IAAIC,oBAAoB,GAAG,KAAK;QAEhC,KAAK,MAAMC,KAAK,IAAIF,OAAO,EAAE;UAC5B,MAAM;YAAE/I,KAAK;YAAE5B;UAAO,CAAC,GAAG6K,KAAK,CAACC,WAAW;UAC3C,MAAMnD,OAAO,GAAGkD,KAAK,CAACvJ,MAAqB;UAC3C,MAAMyJ,SAAS,GAAGC,UAAU,CAACrD,OAAO,CAACsD,OAAO,CAACF,SAAS,IAAI,GAAG,CAAC;UAC9D,MAAMG,UAAU,GAAGF,UAAU,CAACrD,OAAO,CAACsD,OAAO,CAACC,UAAU,IAAI,GAAG,CAAC;UAEhE,IAAI/F,IAAI,CAACgG,GAAG,CAACvJ,KAAK,GAAGmJ,SAAS,CAAC,GAAGL,4BAA4B,IAC7DvF,IAAI,CAACgG,GAAG,CAACnL,MAAM,GAAGkL,UAAU,CAAC,GAAGR,4BAA4B,EAAE;YAC9DE,oBAAoB,GAAG,IAAI;YAC3BjD,OAAO,CAACsD,OAAO,CAACF,SAAS,GAAGnJ,KAAK,CAACwJ,QAAQ,CAAC,CAAC;YAC5CzD,OAAO,CAACsD,OAAO,CAACC,UAAU,GAAGlL,MAAM,CAACoL,QAAQ,CAAC,CAAC;UAC/C;QACD;QAEA,IAAIR,oBAAoB,EAAE;UACzBlB,sBAAsB,CAAC,CAAC;UACxB;UACAM,UAAU,CAACN,sBAAsB,EAAE,EAAE,CAAC;QACvC;MACD,CAAC,CAAC;;MAEF;MACA,CAAClC,UAAU,EAAEF,MAAM,EAAEG,gBAAgB,CAAC,CAAC4D,OAAO,CAAC1D,OAAO,IAAI;QACzD,IAAIA,OAAO,EAAE4C,cAAc,CAAEe,OAAO,CAAC3D,OAAO,CAAC;MAC9C,CAAC,CAAC;IACH;;IAEA;IACA,IAAIH,UAAU,IAAI0B,MAAM,CAACqC,gBAAgB,EAAE;MAC1C,MAAMC,2BAA2B,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;MAEtDhB,gBAAgB,GAAG,IAAIe,gBAAgB,CAAEE,SAAS,IAAK;QACtD,MAAMC,YAAY,GAAGD,SAAS,CAACE,IAAI,CAACC,QAAQ,IAC3CA,QAAQ,CAACC,IAAI,KAAK,WAAW,IAC5BD,QAAQ,CAACC,IAAI,KAAK,YAAY,IAC9BL,2BAA2B,CAAC7G,QAAQ,CAACiH,QAAQ,CAACE,aAAa,IAAI,EAAE,CACnE,CAAC;QAED,IAAIJ,YAAY,EAAE;UACjB1B,UAAU,CAACN,sBAAsB,EAAE,EAAE,CAAC;QACvC;MACD,CAAC,CAAC;MAEFc,gBAAgB,CAACc,OAAO,CAAC9D,UAAU,EAAE;QACpCuE,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAEV;MAClB,CAAC,CAAC;IACH;;IAEA;IACA,OAAO,MAAM;MAAA,IAAAW,eAAA,EAAAC,iBAAA;MACZlD,MAAM,CAACmD,mBAAmB,CAAC,QAAQ,EAAEpC,eAAe,CAAC;MACrDf,MAAM,CAACmD,mBAAmB,CAAC,QAAQ,EAAEpC,eAAe,CAAC;;MAErD;MACA,IAAIxC,gBAAgB,EAAE;QACrBA,gBAAgB,CAAC4E,mBAAmB,CAAC,aAAa,EAAEpC,eAAe,CAAC;QACpExC,gBAAgB,CAAC4E,mBAAmB,CAAC,QAAQ,EAAEpC,eAAe,CAAC;MAChE;;MAEA;MACA,MAAMqC,UAAU,GAAIpD,MAAM,CAASmB,0BAA0B;MAC7D,IAAIiC,UAAU,EAAE;QACflC,aAAa,CAACkC,UAAU,CAAC;QACzB,OAAQpD,MAAM,CAASmB,0BAA0B;MAClD;;MAEA;MACA,CAAA8B,eAAA,GAAA5B,cAAc,cAAA4B,eAAA,uBAAdA,eAAA,CAAgBI,UAAU,CAAC,CAAC;MAC5B,CAAAH,iBAAA,GAAA5B,gBAAgB,cAAA4B,iBAAA,uBAAhBA,iBAAA,CAAkBG,UAAU,CAAC,CAAC;IAC/B,CAAC;EACF,CAAC,EAAE,CAAC1H,IAAI,EAAE3D,mBAAmB,CAAC,CAAC;EAE/B,oBACChE,OAAA,CAAAE,SAAA;IAAAoP,QAAA,GACExO,eAAe,CAACyO,GAAG,CAAE3G,IAAI,IAAK;MAAA,IAAA4G,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,WAAA;MAC9B,MAAMC,QAAQ,IAAAJ,aAAA,GAAG5G,IAAI,CAACiH,MAAM,CAAC,CAAC,CAAC,cAAAL,aAAA,uBAAdA,aAAA,CAAgB5I,GAAG;MACpC,MAAMW,OAAO,IAAAkI,cAAA,GAAG7G,IAAI,CAACiH,MAAM,CAAC,CAAC,CAAC,cAAAJ,cAAA,uBAAdA,cAAA,CAAgBhJ,EAAE;MAClC,MAAMK,SAAS,GAAG,EAAA4I,cAAA,GAAA9G,IAAI,CAACiH,MAAM,CAAC,CAAC,CAAC,cAAAH,cAAA,uBAAdA,cAAA,CAAgB5I,SAAS,KAAInH,cAAc;MAC7D,MAAMmQ,gBAAgB,GAAG,CAAClH,IAAI,aAAJA,IAAI,wBAAA+G,WAAA,GAAJ/G,IAAI,CAAEC,KAAK,cAAA8G,WAAA,uBAAXA,WAAA,CAAa7M,MAAM,KAAetD,4BAA4B;MACxF,MAAMiH,EAAE,GAAGmC,IAAI,CAACnC,EAAE;MAClB,oBACCzG,OAAA,CAAChC,GAAG;QAEH+R,EAAE,EAAE;UACHrL,KAAK,EAAE,MAAM;UACb5B,MAAM,EAAE,MAAM;UACd6B,OAAO,EAAE,MAAM;UACfY,aAAa,EAAE,QAAQ;UACvBX,cAAc,EAAE,YAAY;UAC5BC,UAAU,EAAE,QAAQ;UACpB;UACAE,MAAM,EAAE,KAAK;UACbC,QAAQ,EAAE;QACX,CAAE;QAAAsK,QAAA,eAEFtP,OAAA,CAAChC,GAAG;UACH+R,EAAE,EAAE;YACH,GAAGtL,mBAAmB;YACtBoC,eAAe,EAAE+B,IAAI,CAACC,KAAK,CAAChC,eAAe;YAC3C/D,MAAM,EAAE,GAAG8F,IAAI,CAACC,KAAK,CAAC/F,MAAM;UAC7B,CAAE;UACFkN,OAAO,EAAGC,CAAC,IAAK3I,WAAW,CAAC2I,CAAC,EAAExJ,EAAE,EAAEc,OAAO,EAAEqI,QAAQ,GAAG,IAAI,GAAG,KAAK,EAAEE,gBAAgB,CAAE;UACvFI,SAAS,EAAE,KAAM;UACjBzJ,EAAE,EAAEA,EAAG;UACP0J,WAAW,EAAEA,CAAA,KAAM;YAClBnP,gBAAgB,CAAC;cAChBkG,QAAQ,EAAEK,OAAO;cACjBhB,WAAW,EAAEE,EAAE;cACfpC,KAAK,EAAE;YACR,CAAC,CAAC;UACH,CAAE;UAAAiL,QAAA,EAEDM,QAAQ,gBACR5P,OAAA;YACCoQ,GAAG,EAAER,QAAS;YACdS,GAAG,EAAC,UAAU;YACdxH,KAAK,EAAE;cAAE,GAAG5D,UAAU;cAAE6B;YAAU;UAAE;YAAAwJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,gBAEFzQ,OAAA,CAAChC,GAAG;YACH+R,EAAE,EAAE;cACHW,SAAS,EAAE,QAAQ;cACnBhM,KAAK,EAAE,MAAM;cACb5B,MAAM,EAAE,MAAM;cACd6B,OAAO,EAAE,MAAM;cACfY,aAAa,EAAE,QAAQ;cACvBX,cAAc,EAAE;YACjB,CAAE;YAAA0K,QAAA,gBAEFtP,OAAA,CAAChC,GAAG;cACH+R,EAAE,EAAEzK,aAAc;cAClB4K,SAAS,EAAE,KAAM;cAAAZ,QAAA,gBAEjBtP,OAAA;gBACC2Q,uBAAuB,EAAE;kBAAEC,MAAM,EAAE/R;gBAAW,CAAE;gBAChDgK,KAAK,EAAE;kBAAElE,OAAO,EAAE;gBAAe;cAAE;gBAAA2L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFzQ,OAAA,CAAC/B,UAAU;gBACV4S,OAAO,EAAC,IAAI;gBACZC,KAAK,EAAC,QAAQ;gBACdf,EAAE,EAAE;kBAAEgB,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAM,CAAE;gBAAA1B,QAAA,EAE1C1O,SAAS,CAAC,aAAa;cAAC;gBAAA0P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENzQ,OAAA,CAAC/B,UAAU;cACV4S,OAAO,EAAC,OAAO;cACfC,KAAK,EAAC,QAAQ;cACd1H,KAAK,EAAC,eAAe;cACrB2G,EAAE,EAAE;gBAAEgB,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,EAEvB1O,SAAS,CAAC,4BAA4B;YAAC;cAAA0P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACbzQ,OAAA,CAAC/B,UAAU;cACV4S,OAAO,EAAC,OAAO;cACfC,KAAK,EAAC,QAAQ;cACd1H,KAAK,EAAC,eAAe;cACrB2G,EAAE,EAAE;gBAAE1K,SAAS,EAAE,KAAK;gBAAE0L,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,EAEzC1O,SAAS,CAAC,IAAI;YAAC;cAAA0P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACZtO,kBAAkB,CAACG,MAAM,IAAIH,kBAAkB,CAACE,kBAAkB,KAAKoE,EAAE,gBACzEzG,OAAA,CAAC5B,SAAS;cACTiG,KAAK,EAAE9B,SAAU;cACjB0O,QAAQ,EAAGhB,CAAC,IAAKzN,YAAY,CAACyN,CAAC,CAAC7L,MAAM,CAACC,KAAK,CAAE;cAC9C6M,SAAS,EAAEnI,gBAAiB;cAC5BoI,SAAS;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,gBAEFzQ,OAAA,CAAChC,GAAG;cAAC+R,EAAE,EAAE5K,YAAa;cAAAmK,QAAA,gBACnBtP,OAAA,CAACzB,OAAO;gBAAC6S,KAAK,EAAExQ,SAAS,CAAC,aAAa,CAAE;gBAAA0O,QAAA,eACpDtP,OAAA;kBAAK6I,KAAK,EAAE;oBAAEwI,aAAa,EAAE,MAAM;oBAAEC,MAAM,EAAC;kBAAS,CAAE;kBAAAhC,QAAA,eACrDtP,OAAA;oBACE2Q,uBAAuB,EAAE;sBAAEC,MAAM,EAAE9R;oBAAU,CAAE;oBAC/C+J,KAAK,EAAE;sBACLO,KAAK,EAAE,OAAO;sBACdkI,MAAM,EAAE,SAAS;sBACjBP,QAAQ,EAAE,MAAM;sBAChBQ,OAAO,EAAE,KAAK;sBACdF,aAAa,EAAE;oBACjB,CAAE;oBACF5K,EAAE,EAAC,WAAW;oBACP+K,SAAS,EAAC;kBAAoB;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGGzQ,OAAA,CAACzB,OAAO;gBAAC6S,KAAK,EAAExQ,SAAS,CAAC,aAAa,CAAE;gBAAA0O,QAAA,eACxCtP,OAAA;kBACCgQ,OAAO,EAAG7L,KAAK,IAAK;oBACnB;kBAAA,CACC;kBACLwM,uBAAuB,EAAE;oBAAEC,MAAM,EAAE7R;kBAAM,CAAE;kBAC3C8J,KAAK,EAAE;oBAAEO,KAAK,EAAE,OAAO;oBAAEkI,MAAM,EAAE,SAAS;oBAAEP,QAAQ,EAAE,MAAM;oBAAEQ,OAAO,EAAE;kBAAM,CAAE;kBAC/E9K,EAAE,EAAC,QAAQ;kBACX+K,SAAS,EAAC;kBACV;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACVzQ,OAAA,CAACzB,OAAO;gBAAC6S,KAAK,EAAExQ,SAAS,CAAC,aAAa,CAAE;gBAAA0O,QAAA,eAC3CtP,OAAA;kBACIgQ,OAAO,EAAG7L,KAAK,IAAK;oBAAA,IAAAsN,sBAAA;oBAEtBtN,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuN,eAAe,CAAC,CAAC;oBACxB,CAAAD,sBAAA,GAAAlJ,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,cAAAiJ,sBAAA,uBAAtCA,sBAAA,CAAwChJ,KAAK,CAAC,CAAC;kBAChD,CAAE;kBACFhC,EAAE,EAAC,cAAc;kBACjB+K,SAAS,EAAC,oBAAoB;kBAC9Bb,uBAAuB,EAAE;oBAAEC,MAAM,EAAE5R;kBAAW,CAAE;kBAChD6J,KAAK,EAAE;oBAAEO,KAAK,EAAE,OAAO;oBAAEkI,MAAM,EAAE,SAAS;oBAAEP,QAAQ,EAAE;kBAAO;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACbzQ,OAAA;gBACC2O,IAAI,EAAC,MAAM;gBACXlI,EAAE,EAAC,aAAa;gBAChBoC,KAAK,EAAE;kBAAElE,OAAO,EAAE;gBAAO,CAAE;gBAC3BgN,MAAM,EAAC,SAAS;gBAChBV,QAAQ,EAAEzL;cAAkB;gBAAA8K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFzQ,OAAA,CAACxB,QAAQ;gBAACmJ,IAAI,EAAEnG,YAAa;gBAACoQ,gBAAgB,EAAE,IAAK;gBAACC,OAAO,EAAE3P,aAAc;gBAAC4P,YAAY,EAAE;kBAAEC,QAAQ,EAAE,QAAQ;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAA1C,QAAA,eACxItP,OAAA,CAACvB,KAAK;kBAACoT,OAAO,EAAE3P,aAAc;kBAAC+P,QAAQ,EAAErQ,gBAAiB;kBAACmO,EAAE,EAAE;oBAAErL,KAAK,EAAE;kBAAO,CAAE;kBAAA4K,QAAA,EAC/E5N;gBAAe;kBAAA4O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC,GApJDhK,EAAE;QAAA6J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqJH,CAAC;IAER,CAAC,CAAC,EACDxM,OAAO,CAAClD,aAAa,CAACsD,KAAK,CAAC,gBAC5BrE,OAAA,CAAC9B,OAAO;MACPsT,SAAS,EAAC,sBAAsB;MAChC/K,EAAE,EAAEA,EAAG;MACPkB,IAAI,EAAEA,IAAK;MACXuK,QAAQ,EAAE,IAAK;MACfL,OAAO,EAAEnK,WAAY;MACrByK,eAAe,EAAC,MAAM;MACtBC,SAAS,EAAE;QACVC,KAAK,EAAE;UACNxJ,KAAK,EAAE;YACN,GAAGjF,gBAAgB,CAACE,YAAY;YAChChB,MAAM,EAAE,MAAM;YACd4B,KAAK,EAAE,MAAM;YACbI,OAAO,EAAE,UAAU;YACnBwN,UAAU,EAAE,MAAM;YAClBC,WAAW,EAAE;UACd;QACD;MACD,CAAE;MAAAjD,QAAA,eAEFtP,OAAA,CAAChC,GAAG;QACH+R,EAAE,EAAE;UACHpL,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBO,GAAG,EAAE,MAAM;UACXtC,MAAM,EAAE,MAAM;UACdgC,OAAO,EAAE,QAAQ;UACjBiM,QAAQ,EAAE;QACX,CAAE;QAAAzB,QAAA,gBAEFtP,OAAA,CAAChC,GAAG;UAAC+R,EAAE,EAAE;YAAEpL,OAAO,EAAE;UAAO,CAAE;UAAA2K,QAAA,EAC3B3M,uBAAuB,CAACN,kBAAkB,KAAKtB,aAAa,CAACwF,WAAW,IACzE5D,uBAAuB,CAACE,OAAO,gBAC9B7C,OAAA,CAAAE,SAAA;YAAAoP,QAAA,gBACCtP,OAAA;cAAM2Q,uBAAuB,EAAE;gBAAEC,MAAM,EAAE3R;cAAiB;YAAE;cAAAqR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DzQ,OAAA,CAAC/B,UAAU;cACV8S,QAAQ,EAAC,MAAM;cACfuB,UAAU,EAAE,KAAM;cAClBtC,OAAO,EAAE3H,kBAAmB;cAAAiH,QAAA,EAE1B1O,SAAS,CAAC,eAAe;YAAC;cAAA0P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACbzQ,OAAA;cACC2O,IAAI,EAAC,MAAM;cACXlI,EAAE,EAAC,gBAAgB;cACnBoC,KAAK,EAAE;gBAAElE,OAAO,EAAE;cAAO,CAAE;cAC3BgN,MAAM,EAAC,SAAS;cAChBV,QAAQ,EAAE7J;YAAmB;cAAAkJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA,eACD,CAAC,GACA;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEZzQ,OAAA,CAAChC,GAAG;UACGwT,SAAS,EAAC,kBAAkB;UAC5BzB,EAAE,EAAE;YAAEpL,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAyK,QAAA,gBAE9CtP,OAAA;YAAM2Q,uBAAuB,EAAE;cAAEC,MAAM,EAAExR;YAAc,CAAE;YACzDyJ,KAAK,EAAE;cAAElE,OAAO,EAAE;YAAO;UAAE;YAAA2L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC7BzQ,OAAA,CAACzB,OAAO;YAAC6S,KAAK,EAAEzO,uBAAuB,CAACG,MAAM,IAAIpD,wBAAwB,GAAGkB,SAAS,CAAC,wBAAwB,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAE;YAAA0O,QAAA,eAC/ItP,OAAA;cAAAsP,QAAA,eACCtP,OAAA,CAAC7B,UAAU;gBACV6R,OAAO,EAAEA,CAAA,KAAM7H,oBAAoB,CAACxF,uBAAuB,CAACG,MAAM,CAAE;gBACpEyI,IAAI,EAAC,OAAO;gBACZiH,QAAQ,EAAE7P,uBAAuB,CAACG,MAAM,IAAIpD,wBAAyB;gBACrEqQ,EAAE,EAAE;kBACHwB,OAAO,EAAE5O,uBAAuB,CAACG,MAAM,IAAIpD,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7E4R,MAAM,EAAE3O,uBAAuB,CAACG,MAAM,IAAIpD,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAA4P,QAAA,eAEFtP,OAAA,CAACtB,UAAU;kBAACqS,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACVzQ,OAAA,CAAC/B,UAAU;YAAC8S,QAAQ,EAAC,MAAM;YAAAzB,QAAA,EAAE3M,uBAAuB,CAACG;UAAM;YAAAwN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzEzQ,OAAA,CAACzB,OAAO;YAAC6S,KAAK,EAAEzO,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAGmB,SAAS,CAAC,wBAAwB,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAE;YAAA0O,QAAA,eAC/ItP,OAAA;cAAAsP,QAAA,eACCtP,OAAA,CAAC7B,UAAU;gBACV6R,OAAO,EAAEA,CAAA,KAAMlI,oBAAoB,CAACnF,uBAAuB,CAACG,MAAM,CAAE;gBACpEyI,IAAI,EAAC,OAAO;gBACZiH,QAAQ,EAAE7P,uBAAuB,CAACG,MAAM,IAAIrD,wBAAyB;gBACrEsQ,EAAE,EAAE;kBACHwB,OAAO,EAAE5O,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7E6R,MAAM,EAAE3O,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAA6P,QAAA,eAEFtP,OAAA,CAACrB,OAAO;kBAACoS,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNzQ,OAAA,CAACzB,OAAO;UAAC6S,KAAK,EAAExQ,SAAS,CAAC,UAAU,CAAE;UAAA0O,QAAA,eACtCtP,OAAA,CAAChC,GAAG;YAACwT,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,gBAChCtP,OAAA,CAAChC,GAAG;cAACwT,SAAS,EAAC,kBAAkB;cAAAlC,QAAA,eAChCtP,OAAA,CAAC7B,UAAU;gBACVoN,IAAI,EAAC,OAAO;gBACZyE,OAAO,EAAE1L,mBAAoB;gBAAAgL,QAAA,eAE7BtP,OAAA;kBACC2Q,uBAAuB,EAAE;oBAAEC,MAAM,EAAEvR;kBAAS,CAAE;kBAC9CwJ,KAAK,EAAE;oBAAEO,KAAK,EAAE;kBAAO;gBAAE;kBAAAkH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAELzQ,OAAA,CAAC9B,OAAO;cACEsT,SAAS,EAAC,cAAc;cAClC7J,IAAI,EAAE3D,mBAAoB;cAC1BkO,QAAQ,EAAE,IAAK;cACfL,OAAO,EAAErN,0BAA2B;cACpC2N,eAAe,EAAC,MAAM;cACtBC,SAAS,EAAE;gBACVC,KAAK,EAAE;kBACNxJ,KAAK,EAAE;oBACN,GAAGjF,gBAAgB,CAACG,eAAe;oBACnCW,KAAK,EAAE;kBACR;gBACD;cACD,CAAE;cAAA4K,QAAA,eAEFtP,OAAA,CAAChC,GAAG;gBAACyU,CAAC,EAAE,CAAE;gBAAAnD,QAAA,gBACTtP,OAAA,CAAChC,GAAG;kBACH2G,OAAO,EAAC,MAAM;kBACdC,cAAc,EAAC,eAAe;kBAC9BC,UAAU,EAAC,QAAQ;kBAAAyK,QAAA,gBAEnBtP,OAAA,CAAC/B,UAAU;oBACV4S,OAAO,EAAC,WAAW;oBACnBd,EAAE,EAAE;sBAAE3G,KAAK,EAAE;oBAAwB,CAAE;oBAAAkG,QAAA,EAErC1O,SAAS,CAAC,kBAAkB;kBAAC;oBAAA0P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACbzQ,OAAA,CAAC7B,UAAU;oBACVoN,IAAI,EAAC,OAAO;oBACZyE,OAAO,EAAExL,0BAA2B;oBAAA8K,QAAA,eAEpCtP,OAAA;sBACC2Q,uBAAuB,EAAE;wBAAEC,MAAM,EAAEtR;sBAAU,CAAE;sBAC/CuJ,KAAK,EAAE;wBAAEO,KAAK,EAAE;sBAAQ;oBAAE;sBAAAkH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACNzQ,OAAA,CAACzB,OAAO;kBAAC6S,KAAK,EAAExQ,SAAS,CAAC,aAAa,CAAE;kBAAA0O,QAAA,eAC1CtP,OAAA,CAAChC,GAAG;oBAAC0U,EAAE,EAAE,CAAE;oBAAApD,QAAA,gBACVtP,OAAA,CAAC/B,UAAU;sBACV4S,OAAO,EAAC,OAAO;sBACbzH,KAAK,EAAC,eAAe;sBACrB2G,EAAE,EAAE;wBAAE4C,YAAY,EAAE;sBAAO,CAAE;sBAAArD,QAAA,EAE5B1O,SAAS,CAAC,eAAe;oBAAC;sBAAA0P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACbzQ,OAAA,CAAC5B,SAAS;sBACTwU,MAAM;sBACNC,SAAS;sBACThC,OAAO,EAAC,UAAU;sBAClBtF,IAAI,EAAC,OAAO;sBACZlH,KAAK,EAAEtB,cAAe;sBACtBkO,QAAQ,EAAE/M,kBAAmB;sBAC7B6L,EAAE,EAAE;wBACH,0BAA0B,EAAE;0BAC3B+C,WAAW,EAAE;wBACd;sBACD,CAAE;sBACFN,QAAQ;sBAAAlD,QAAA,gBAENtP,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,MAAM;wBAAAiL,QAAA,EAAE1O,SAAS,CAAC,MAAM;sBAAC;wBAAA0P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACrDzQ,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,cAAc;wBAAAiL,QAAA,EAAE1O,SAAS,CAAC,eAAe;sBAAC;wBAAA0P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtEzQ,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,SAAS;wBAAAiL,QAAA,EAAE1O,SAAS,CAAC,UAAU;sBAAC;wBAAA0P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAC5DzQ,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,cAAc;wBAAAiL,QAAA,EAAE1O,SAAS,CAAC,eAAe;sBAAC;wBAAA0P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtEzQ,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,WAAW;wBAAAiL,QAAA,EAAE1O,SAAS,CAAC,YAAY;sBAAC;wBAAA0P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAChEzQ,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,kBAAkB;wBAAAiL,QAAA,EAAE1O,SAAS,CAAC,oBAAoB;sBAAC;wBAAA0P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACVzQ,OAAA,CAAChC,GAAG;kBAAC0U,EAAE,EAAE,CAAE;kBAAApD,QAAA,gBACVtP,OAAA,CAAC/B,UAAU;oBACV4S,OAAO,EAAC,OAAO;oBACfzH,KAAK,EAAC,eAAe;oBAAAkG,QAAA,EAEnB1O,SAAS,CAAC,kBAAkB;kBAAC;oBAAA0P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACbzQ,OAAA,CAAChC,GAAG;oBACH2G,OAAO,EAAC,MAAM;oBACdS,GAAG,EAAE,CAAE;oBACPsN,EAAE,EAAE,CAAE;oBAAApD,QAAA,EAEL,CAAC,MAAM,EAAE,KAAK,CAAC,CAACC,GAAG,CAAE3G,IAAI,IAAK;sBAC9B;sBACA,MAAMmK,gBAAgB,GAAGjS,eAAe,CAAC6H,IAAI,CAAEqK,CAAC,IAAKA,CAAC,CAACvM,EAAE,KAAK1F,aAAa,CAACwF,WAAW,CAAC;sBACxF,MAAM0M,YAAY,GAAGF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAElD,MAAM,CAAClH,IAAI,CAAEuK,GAAG,IAAKA,GAAG,CAACzM,EAAE,KAAK1F,aAAa,CAACmG,QAAQ,CAAC;sBAC9F,MAAMiM,gBAAgB,GAAG,CAAAF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEnM,SAAS,KAAInH,cAAc;;sBAElE;sBACA,MAAMyT,UAAU,GAAIxK,IAAI,KAAK,MAAM,IAAIuK,gBAAgB,KAAK,OAAO,IAC5DvK,IAAI,KAAK,KAAK,IAAIuK,gBAAgB,KAAK,SAAU;sBAExD,oBACCnT,OAAA,CAAC1B,MAAM;wBAEN0R,OAAO,EAAEA,CAAA,KACR3O,SAAS,CAACN,aAAa,CAACwF,WAAW,EAAExF,aAAa,CAACmG,QAAQ,EAAE0B,IAAsB,CACnF;wBACDiI,OAAO,EAAC,UAAU;wBAClBtF,IAAI,EAAC,OAAO;wBACZwE,EAAE,EAAE;0BACHrL,KAAK,EAAE,QAAQ;0BACf5B,MAAM,EAAE,MAAM;0BACdgC,OAAO,EAAE,WAAW;0BACpBM,GAAG,EAAE,MAAM;0BACXF,YAAY,EAAE,iBAAiB;0BAC/BmO,MAAM,EACLD,UAAU,GACP,iCAAiC,GACjC,kCAAkC;0BACtCvM,eAAe,EACduM,UAAU,GAAG,yBAAyB,GAAG,0BAA0B;0BACpEE,mBAAmB,EAAE,UAAU;0BAC/BlK,KAAK,EAAE,OAAO;0BACd,SAAS,EAAE;4BACVvC,eAAe,EACduM,UAAU,GAAG,yBAAyB,GAAG;0BAC3C;wBACD,CAAE;wBAAA9D,QAAA,EAED1O,SAAS,CAACgI,IAAI;sBAAC,GA1BXA,IAAI;wBAAA0H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2BF,CAAC;oBAEX,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACVzQ,OAAA,CAACzB,OAAO;UAAC6S,KAAK,EAAExQ,SAAS,CAAC,kBAAkB,CAAE;UAAA0O,QAAA,eAC9CtP,OAAA,CAAChC,GAAG;YAACwT,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChCtP,OAAA,CAAC7B,UAAU;cACV6R,OAAO,EAAE1G,0BAA2B;cACpCiC,IAAI,EAAC,OAAO;cAAA+D,QAAA,eAEZtP,OAAA;gBACA6I,KAAK,EAAE;kBACNhC,eAAe,EAAEtD,aAAa;kBAC9B2B,YAAY,EAAE,MAAM;kBACpBR,KAAK,EAAE,MAAM;kBACb5B,MAAM,EAAE;gBACT;cAAE;gBAAAwN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACVzQ,OAAA,CAACzB,OAAO;UAAC6S,KAAK,EAAE5Q,eAAe,GAAGI,SAAS,CAAC,2CAA2C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;UAAA0O,QAAA,eACtHtP,OAAA,CAAChC,GAAG;YAACwT,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChCtP,OAAA,CAAC7B,UAAU;cACV6R,OAAO,EAAE/G,uBAAwB;cACjCsC,IAAI,EAAC,OAAO;cACZiH,QAAQ,EAAEhS,eAAgB;cAAA8O,QAAA,eAE1BtP,OAAA;gBACC2Q,uBAAuB,EAAE;kBAAEC,MAAM,EAAE1R;gBAAS,CAAE;gBAC9C2J,KAAK,EAAE;kBAAE0I,OAAO,EAAE/Q,eAAe,GAAG,GAAG,GAAG;gBAAE;cAAE;gBAAA8P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACVzQ,OAAA,CAACzB,OAAO;UAAC6S,KAAK,EAAExQ,SAAS,CAAC,gBAAgB,CAAE;UAAA0O,QAAA,eAE5CtP,OAAA,CAAChC,GAAG;YAACwT,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChCtP,OAAA,CAAC7B,UAAU;cACV6R,OAAO,EAAElH,mBAAoB;cAC7ByC,IAAI,EAAC,OAAO;cAAA+D,QAAA,eAEZtP,OAAA;gBAAM2Q,uBAAuB,EAAE;kBAAEC,MAAM,EAAEzR;gBAAW;cAAE;gBAAAmR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GACP,IAAI,EAEPxN,WAAW,iBACVjD,OAAA,CAACF,0BAA0B;MAACwC,MAAM,EAAEW,WAAY;MAACsQ,gBAAgB,EAAEA,CAAA,KAAMrQ,YAAY,CAAC,KAAK,CAAE;MAACsQ,aAAa,EAAExM,wBAAyB;MAAC5D,eAAe,EAAEA,eAAgB;MAACD,YAAY,EAAEA,YAAa;MAACiE,kBAAkB,EAAEA,kBAAmB;MAAC3D,cAAc,EAAEA;IAAe;MAAA6M,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAC7Q,eAEFzQ,OAAA,CAAC9B,OAAO;MACPyJ,IAAI,EAAEC,eAAgB;MACtBsK,QAAQ,EAAEzP,mBAAoB;MAC9BoP,OAAO,EAAE3I,sBAAuB;MAChC4I,YAAY,EAAE;QACbC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACFyB,eAAe,EAAE;QAChB1B,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MAAA1C,QAAA,eAEFtP,OAAA,CAAChC,GAAG;QAAAsR,QAAA,gBACHtP,OAAA,CAACH,YAAY;UACZuJ,KAAK,EAAEV,qBAAsB;UAC7BuI,QAAQ,EAAE9H;QAAkB;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACAzQ,OAAA;UAAAsP,QAAA,EACF;AACL;AACA;AACA;AACA;QAAK;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACT,CAAC;AAEL,CAAC;AAAChQ,EAAA,CApiCIN,YAAY;EAAA,QACQvB,cAAc,EAYnCW,cAAc;AAAA;AAAAmU,EAAA,GAbbvT,YAAY;AAsiClB,eAAeA,YAAY;AAAC,IAAAuT,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}