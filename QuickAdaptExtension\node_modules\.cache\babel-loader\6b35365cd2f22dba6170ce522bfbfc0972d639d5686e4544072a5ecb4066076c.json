{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\Imagesection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip, Snackbar, Alert } from \"@mui/material\";\nimport RemoveIcon from \"@mui/icons-material/Remove\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport { useTranslation } from 'react-i18next';\nimport { uploadfile, hyperlink, files, uploadicon, replaceimageicon, copyicon, deleteicon, sectionheight, Settings, CrossIcon } from \"../../../assets/icons/icons\";\nimport useDrawerStore, { IMG_CONTAINER_DEFAULT_HEIGHT, IMG_CONTAINER_MAX_HEIGHT, IMG_CONTAINER_MIN_HEIGHT, IMG_OBJECT_FIT, IMG_STEP_VALUE } from \"../../../store/drawerStore\";\nimport { ChromePicker } from \"react-color\";\nimport \"./PopupSections.css\";\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageSection = ({\n  setImageSrc,\n  setImageName,\n  onDelete,\n  onClone,\n  isCloneDisabled\n}) => {\n  _s();\n  var _imagesContainer$find;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    uploadImage,\n    imagesContainer,\n    imageAnchorEl,\n    setImageAnchorEl,\n    replaceImage,\n    cloneImageContainer,\n    deleteImageContainer,\n    updateImageContainer,\n    toggleFit,\n    setImageSrc: storeImageSrc\n  } = useDrawerStore(state => state);\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState('');\n  const [snackbarSeverity, setSnackbarSeverity] = useState('info');\n  const [snackbarKey, setSnackbarKey] = useState(0);\n  const openSnackbar = () => {\n    setSnackbarKey(prev => prev + 1);\n    setSnackbarOpen(true);\n  };\n  const closeSnackbar = () => {\n    setSnackbarOpen(false);\n  };\n  const [showHyperlinkInput, setShowHyperlinkInput] = useState({\n    currentContainerId: \"\",\n    isOpen: false\n  });\n  const [imageLink, setImageLink] = useState(\"\");\n  const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState(null);\n  const [currentImageSectionInfo, setCurrentImageSectionInfo] = useState({\n    currentContainerId: \"\",\n    isImage: false,\n    height: IMG_CONTAINER_DEFAULT_HEIGHT\n  });\n  const [selectedAction, setSelectedAction] = useState(\"none\");\n  const [isModelOpen, setModelOpen] = useState(false);\n  const [formOfUpload, setFormOfUpload] = useState(\"\");\n  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);\n  const [selectedColor, setSelectedColor] = useState(\"#313030\");\n  const [isReplaceImage, setReplaceImage] = useState(false);\n  const guidePopupRef = useRef(null);\n  const [popoverPositions, setPopoverPositions] = useState({\n    imagePopover: {},\n    settingsPopover: {}\n  });\n  const openSettingsPopover = Boolean(settingsAnchorEl);\n  const handleActionChange = event => {\n    setSelectedAction(event.target.value);\n  };\n  const handleSettingsClick = event => {\n    setSettingsAnchorEl(event.currentTarget);\n  };\n  const handleCloseSettingsPopover = () => {\n    setSettingsAnchorEl(null);\n  };\n  const imageContainerStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    padding: 0,\n    margin: 0,\n    overflow: \"hidden\"\n  };\n  const imageStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    margin: 0,\n    padding: 0,\n    borderRadius: \"0\"\n  };\n  const iconRowStyle = {\n    display: \"flex\",\n    justifyContent: \"center\",\n    gap: \"16px\",\n    marginTop: \"10px\"\n  };\n  const iconTextStyle = {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    width: \"100%\"\n  };\n  const handleImageUpload = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      var _event$target$files2;\n      const parts = file.name.split('.');\n      const extension = parts.pop();\n\n      // Check for double extensions (e.g. file.html.png) or missing/invalid extension\n      if (parts.length > 1 || !extension) {\n        setSnackbarMessage(\"Uploaded file name should not contain any special character\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      if (file.name.length > 128) {\n        setSnackbarMessage(\"File name should not exceed 128 characters\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      setImageName((_event$target$files2 = event.target.files) === null || _event$target$files2 === void 0 ? void 0 : _event$target$files2[0].name);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        const base64Image = reader.result;\n        storeImageSrc(base64Image);\n        setImageSrc(base64Image);\n        uploadImage(imageAnchorEl.containerId, {\n          altText: file.name,\n          id: crypto.randomUUID(),\n          url: base64Image,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n    setModelOpen(false);\n  };\n  const handleImageUploadFormApp = file => {\n    if (file) {\n      storeImageSrc(file.Url);\n      setImageSrc(file.Url);\n      if (isReplaceImage) {\n        replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\n          altText: file.FileName,\n          id: imageAnchorEl.buttonId,\n          url: file.Url,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n        setReplaceImage(false);\n      } else {\n        uploadImage(imageAnchorEl.containerId, {\n          altText: file.FileName,\n          id: crypto.randomUUID(),\n          // Use existing ID\n          url: file.Url,\n          // Directly use the URL\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      }\n    }\n    setModelOpen(false);\n  };\n  const handleReplaceImage = event => {\n    var _event$target$files3;\n    const file = (_event$target$files3 = event.target.files) === null || _event$target$files3 === void 0 ? void 0 : _event$target$files3[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\n          altText: file.name,\n          id: imageAnchorEl.buttonId,\n          url: reader.result,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleClick = (event, containerId, imageId, isImage, currentHeight) => {\n    // @ts-ignore\n    if ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\n    setImageAnchorEl({\n      buttonId: imageId,\n      containerId: containerId,\n      // @ts-ignore\n      value: event.currentTarget\n    });\n    setSettingsAnchorEl(null);\n    setCurrentImageSectionInfo({\n      currentContainerId: containerId,\n      isImage,\n      height: currentHeight\n    });\n    setShowHyperlinkInput({\n      currentContainerId: \"\",\n      isOpen: false\n    });\n  };\n  const handleClose = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      // @ts-ignore\n      value: null\n    });\n  };\n  const open = Boolean(imageAnchorEl.value);\n  const colorPickerOpen = Boolean(colorPickerAnchorEl);\n  const id = open ? \"image-popover\" : undefined;\n  const handleIncreaseHeight = prevHeight => {\n    if (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\n    const newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const handleDecreaseHeight = prevHeight => {\n    if (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\n    const newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const triggerImageUpload = () => {\n    var _document$getElementB;\n    (_document$getElementB = document.getElementById(\"replace-upload\")) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.click();\n    // setModelOpen(true);\n    // setReplaceImage(true);\n  };\n  const currentContainerColor = ((_imagesContainer$find = imagesContainer.find(item => item.id === imageAnchorEl.containerId)) === null || _imagesContainer$find === void 0 ? void 0 : _imagesContainer$find.style.backgroundColor) || \"transparent\";\n  // Function to delete the section\n  const handleDeleteSection = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      // @ts-ignore\n      value: null\n    });\n\n    // Call the delete function from the store\n    deleteImageContainer(imageAnchorEl.containerId);\n\n    // Call the onDelete callback if provided\n    if (onDelete) {\n      onDelete();\n    }\n  };\n  const handleLinkSubmit = event => {\n    if (event.key === \"Enter\" && imageLink) {\n      uploadImage(imageAnchorEl.containerId, {\n        altText: \"New Image\",\n        id: crypto.randomUUID(),\n        url: imageLink,\n        backgroundColor: \"transparent\",\n        objectFit: IMG_OBJECT_FIT\n      });\n      setShowHyperlinkInput({\n        currentContainerId: \"\",\n        isOpen: false\n      });\n    }\n  };\n  const handleCloneImgContainer = () => {\n    // Check if cloning is disabled due to section limits\n    if (isCloneDisabled) {\n      return; // Don't clone if limit is reached\n    }\n\n    // Call the clone function from the store\n    cloneImageContainer(imageAnchorEl.containerId);\n\n    // Call the onClone callback if provided\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleCloseColorPicker = () => {\n    setColorPickerAnchorEl(null);\n  };\n  const handleColorChange = color => {\n    setSelectedColor(color.hex);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      backgroundColor: color.hex\n    });\n  };\n  const handleBackgroundColorClick = event => {\n    setColorPickerAnchorEl(event.currentTarget);\n  };\n  const getGuidePopupPosition = () => {\n    const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') || document.getElementById('guide-popup');\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      return {\n        top: rect.top,\n        left: rect.left,\n        width: rect.width,\n        height: rect.height\n      };\n    }\n    return null;\n  };\n  const getImagePopoverPosition = () => {\n    const guidePopupPos = getGuidePopupPosition();\n    if (!guidePopupPos) return {};\n    const popoverHeight = 40;\n    const requiredGap = 10;\n    const minTopMargin = 8;\n    const viewportHeight = window.innerHeight;\n    const popoverWidth = 500;\n    const viewportWidth = window.innerWidth;\n\n    // Calculate preferred position above GuidePopup\n    const positionWithGap = guidePopupPos.top - popoverHeight - requiredGap;\n\n    // Get banner element and its position for collision detection\n    const bannerElement = document.querySelector('.qadpt-ext-banner');\n    let wouldCollideWithBanner = false;\n    if (bannerElement) {\n      const bannerBottom = bannerElement.getBoundingClientRect().bottom;\n      // Account for GuidePopup border thickness (5px-10px) and add small safety margin\n      const borderAndSafetyMargin = 15; // 10px max border + 5px safety margin\n      const minClearanceFromBanner = bannerBottom + borderAndSafetyMargin;\n\n      // Only consider collision if the popover would actually overlap with banner area\n      wouldCollideWithBanner = positionWithGap < minClearanceFromBanner;\n    }\n    let topPosition;\n    let isPositionedOnTop = false; // Track if we're using fallback positioning\n\n    if (wouldCollideWithBanner) {\n      // Fallback: Position on top of GuidePopup instead of above it\n      topPosition = guidePopupPos.top + requiredGap;\n      isPositionedOnTop = true;\n    } else if (positionWithGap >= minTopMargin) {\n      // Standard positioning above GuidePopup\n      topPosition = positionWithGap;\n    } else {\n      // Limited space above - use progressive gap reduction\n      const availableSpaceAbove = guidePopupPos.top - popoverHeight - minTopMargin;\n      const gaps = [15, 10, 5, 2];\n      const gap = gaps.find(g => availableSpaceAbove >= g) || 2;\n      topPosition = gap === 2 ? Math.max(minTopMargin, guidePopupPos.top - popoverHeight - 2) : guidePopupPos.top - popoverHeight - gap;\n    }\n\n    // Viewport boundary checks\n    const maxTopPosition = viewportHeight - popoverHeight;\n    topPosition = Math.min(topPosition, maxTopPosition);\n\n    // Ensure we don't go below the minimum top margin (except for on-top positioning)\n    if (!isPositionedOnTop) {\n      topPosition = Math.max(topPosition, minTopMargin);\n    }\n\n    // Calculate horizontal position - center relative to GuidePopUp\n    let leftPosition = guidePopupPos.left + guidePopupPos.width / 2 - 250;\n    leftPosition = Math.max(10, Math.min(leftPosition, viewportWidth - popoverWidth - 10));\n    return {\n      top: topPosition,\n      left: leftPosition,\n      position: 'fixed',\n      zIndex: 999999\n    };\n  };\n  const getSettingsPopoverPosition = () => {\n    const guidePopupPos = getGuidePopupPosition();\n    if (!guidePopupPos) return {};\n    const viewportHeight = window.innerHeight;\n    const viewportWidth = window.innerWidth;\n    const settingsPopupHeight = 200;\n    const settingsPopupWidth = 300;\n    let leftPosition = guidePopupPos.left + guidePopupPos.width + 10;\n    let topPosition = guidePopupPos.top + guidePopupPos.height / 2 - settingsPopupHeight / 2 + 10;\n    if (leftPosition + settingsPopupWidth > viewportWidth - 10) {\n      leftPosition = guidePopupPos.left - settingsPopupWidth - 10; // Position to the left instead\n    }\n    topPosition = Math.max(10, Math.min(topPosition, viewportHeight - settingsPopupHeight - 10));\n    leftPosition = Math.max(10, Math.min(leftPosition, viewportWidth - settingsPopupWidth - 10));\n    return {\n      top: topPosition,\n      left: leftPosition,\n      position: 'fixed',\n      zIndex: 999999\n    };\n  };\n  const updatePopoverPositions = () => {\n    setPopoverPositions({\n      imagePopover: getImagePopoverPosition(),\n      settingsPopover: getSettingsPopoverPosition()\n    });\n  };\n  useEffect(() => {\n    const handlePositionUpdate = () => updatePopoverPositions();\n    if (open || openSettingsPopover) {\n      updatePopoverPositions();\n      const positionCheckInterval = setInterval(() => {\n        if (open || openSettingsPopover) {\n          updatePopoverPositions();\n        } else {\n          clearInterval(positionCheckInterval);\n        }\n      }, 200);\n      window.qadptPositionCheckInterval = positionCheckInterval;\n    }\n    window.addEventListener('resize', handlePositionUpdate);\n    window.addEventListener('scroll', handlePositionUpdate);\n    const perfectScrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\n    if (perfectScrollbarElement) {\n      perfectScrollbarElement.addEventListener('ps-scroll-y', handlePositionUpdate);\n      perfectScrollbarElement.addEventListener('scroll', handlePositionUpdate);\n    }\n    const elementsToObserve = [document.getElementById('guide-popup'), document.querySelector('.qadpt-guide-popup .MuiDialog-paper'), document.querySelector('.qadpt-guide-popup .ps')].filter(Boolean);\n    let resizeObserver = null;\n    let mutationObserver = null;\n    if (window.ResizeObserver && elementsToObserve.length > 0) {\n      resizeObserver = new ResizeObserver(entries => {\n        const hasSignificantChange = entries.some(entry => {\n          const {\n            width,\n            height\n          } = entry.contentRect;\n          const element = entry.target;\n          const lastWidth = parseFloat(element.dataset.lastWidth || '0');\n          const lastHeight = parseFloat(element.dataset.lastHeight || '0');\n          if (Math.abs(width - lastWidth) > 1 || Math.abs(height - lastHeight) > 1) {\n            element.dataset.lastWidth = width.toString();\n            element.dataset.lastHeight = height.toString();\n            return true;\n          }\n          return false;\n        });\n        if (hasSignificantChange) {\n          updatePopoverPositions();\n          setTimeout(updatePopoverPositions, 50);\n        }\n      });\n      elementsToObserve.forEach(element => resizeObserver.observe(element));\n    }\n    const guidePopupElement = elementsToObserve[0];\n    if (guidePopupElement && window.MutationObserver) {\n      mutationObserver = new MutationObserver(mutations => {\n        const shouldUpdate = mutations.some(mutation => {\n          return mutation.type === 'childList' || mutation.type === 'attributes' && ['style', 'class'].includes(mutation.attributeName || '');\n        });\n        if (shouldUpdate) {\n          setTimeout(updatePopoverPositions, 50);\n        }\n      });\n      mutationObserver.observe(guidePopupElement, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: ['style', 'class']\n      });\n    }\n    return () => {\n      var _resizeObserver, _mutationObserver;\n      window.removeEventListener('resize', handlePositionUpdate);\n      window.removeEventListener('scroll', handlePositionUpdate);\n      const perfectScrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\n      if (perfectScrollbarElement) {\n        perfectScrollbarElement.removeEventListener('ps-scroll-y', handlePositionUpdate);\n        perfectScrollbarElement.removeEventListener('scroll', handlePositionUpdate);\n      }\n      const intervalId = window.qadptPositionCheckInterval;\n      if (intervalId) {\n        clearInterval(intervalId);\n        delete window.qadptPositionCheckInterval;\n      }\n      (_resizeObserver = resizeObserver) === null || _resizeObserver === void 0 ? void 0 : _resizeObserver.disconnect();\n      (_mutationObserver = mutationObserver) === null || _mutationObserver === void 0 ? void 0 : _mutationObserver.disconnect();\n    };\n  }, [open, openSettingsPopover]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [imagesContainer.map(item => {\n      var _item$images$, _item$images$2, _item$images$3, _item$style;\n      const imageSrc = (_item$images$ = item.images[0]) === null || _item$images$ === void 0 ? void 0 : _item$images$.url;\n      const imageId = (_item$images$2 = item.images[0]) === null || _item$images$2 === void 0 ? void 0 : _item$images$2.id;\n      const objectFit = ((_item$images$3 = item.images[0]) === null || _item$images$3 === void 0 ? void 0 : _item$images$3.objectFit) || IMG_OBJECT_FIT;\n      const currentSecHeight = (item === null || item === void 0 ? void 0 : (_item$style = item.style) === null || _item$style === void 0 ? void 0 : _item$style.height) || IMG_CONTAINER_DEFAULT_HEIGHT;\n      const id = item.id;\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: \"100%\",\n          height: \"100%\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          justifyContent: \"flex-start\",\n          alignItems: \"center\",\n          margin: \"0px\",\n          overflow: \"auto\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            ...imageContainerStyle,\n            backgroundColor: item.style.backgroundColor,\n            height: `${item.style.height}px`\n          },\n          onClick: e => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight),\n          component: \"div\",\n          id: id,\n          onMouseOver: () => {\n            setImageAnchorEl({\n              buttonId: imageId,\n              containerId: id,\n              value: null\n            });\n          },\n          children: imageSrc ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: imageSrc,\n            alt: \"Uploaded\",\n            style: {\n              ...imageStyle,\n              objectFit\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 9\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: \"center\",\n              width: \"100%\",\n              height: \"100%\",\n              display: \"flex\",\n              flexDirection: \"column\",\n              justifyContent: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: iconTextStyle,\n              component: \"div\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: uploadfile\n                },\n                style: {\n                  display: \"inline-block\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                align: \"center\",\n                sx: {\n                  fontSize: \"14px\",\n                  fontWeight: \"600\"\n                },\n                children: translate(\"Upload file\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              align: \"center\",\n              color: \"textSecondary\",\n              sx: {\n                fontSize: \"14px\"\n              },\n              children: translate(\"Drag & Drop to upload file\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              align: \"center\",\n              color: \"textSecondary\",\n              sx: {\n                marginTop: \"8px\",\n                fontSize: \"14px\"\n              },\n              children: translate(\"Or\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 10\n            }, this), showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? /*#__PURE__*/_jsxDEV(TextField, {\n              value: imageLink,\n              onChange: e => setImageLink(e.target.value),\n              onKeyDown: handleLinkSubmit,\n              autoFocus: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 11\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: iconRowStyle,\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: translate(\"Coming soon\"),\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    pointerEvents: \"auto\",\n                    cursor: \"pointer\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: hyperlink\n                    },\n                    style: {\n                      color: \"black\",\n                      cursor: \"pointer\",\n                      fontSize: \"32px\",\n                      opacity: \"0.5\",\n                      pointerEvents: \"none\"\n                    },\n                    id: \"hyperlink\",\n                    className: \"qadpt-image-upload\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 5\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 3\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: translate(\"Coming soon\"),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  onClick: event => {\n                    //setModelOpen(true);\n                  },\n                  dangerouslySetInnerHTML: {\n                    __html: files\n                  },\n                  style: {\n                    color: \"black\",\n                    cursor: \"pointer\",\n                    fontSize: \"32px\",\n                    opacity: \"0.5\"\n                  },\n                  id: \"folder\",\n                  className: \"qadpt-image-upload\"\n                  //title=\"Coming Soon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: translate(\"Upload File\"),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  onClick: event => {\n                    var _document$getElementB2;\n                    event === null || event === void 0 ? void 0 : event.stopPropagation();\n                    (_document$getElementB2 = document.getElementById(\"file-upload\")) === null || _document$getElementB2 === void 0 ? void 0 : _document$getElementB2.click();\n                  },\n                  id: \"file-upload1\",\n                  className: \"qadpt-image-upload\",\n                  dangerouslySetInnerHTML: {\n                    __html: uploadicon\n                  },\n                  style: {\n                    color: \"black\",\n                    cursor: \"pointer\",\n                    fontSize: \"32px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 12\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"file-upload\",\n                style: {\n                  display: \"none\"\n                },\n                accept: \"image/*\",\n                onChange: handleImageUpload\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 12\n              }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n                open: snackbarOpen,\n                autoHideDuration: 3000,\n                onClose: closeSnackbar,\n                anchorOrigin: {\n                  vertical: 'bottom',\n                  horizontal: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(Alert, {\n                  onClose: closeSnackbar,\n                  severity: snackbarSeverity,\n                  sx: {\n                    width: '100%'\n                  },\n                  children: snackbarMessage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 13\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 12\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 7\n        }, this)\n      }, id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 6\n      }, this);\n    }), Boolean(imageAnchorEl.value) ? /*#__PURE__*/_jsxDEV(Popover, {\n      className: \"qadpt-imgsec-popover\",\n      id: id,\n      open: open,\n      anchorEl: null,\n      onClose: handleClose,\n      anchorReference: \"none\",\n      slotProps: {\n        paper: {\n          style: {\n            ...popoverPositions.imagePopover,\n            height: 'auto',\n            width: 'auto',\n            padding: '5px 10px',\n            marginLeft: 'auto',\n            marginRight: 'auto'\n          }\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"15px\",\n          height: \"100%\",\n          padding: \"0 10px\",\n          fontSize: \"12px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\"\n          },\n          children: currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId && currentImageSectionInfo.isImage ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: replaceimageicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 750,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              fontSize: \"12px\",\n              marginLeft: \"5px\",\n              onClick: triggerImageUpload,\n              children: translate(\"Replace Image\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"replace-upload\",\n              style: {\n                display: \"none\"\n              },\n              accept: \"image/*\",\n              onChange: handleReplaceImage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 10\n            }, this)]\n          }, void 0, true) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-tool-items\",\n          sx: {\n            display: \"flex\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: sectionheight\n            },\n            style: {\n              display: \"flex\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\") : translate(\"Decrease height\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleDecreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 775,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            fontSize: \"12px\",\n            children: currentImageSectionInfo.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\") : translate(\"Increase height\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleIncreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 1\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Settings\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-tool-items\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: handleSettingsClick,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: Settings\n                  },\n                  style: {\n                    color: \"black\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Popover, {\n              className: \"qadpt-imgset\",\n              open: openSettingsPopover,\n              anchorEl: null,\n              onClose: handleCloseSettingsPopover,\n              anchorReference: \"none\",\n              slotProps: {\n                paper: {\n                  style: {\n                    ...popoverPositions.settingsPopover,\n                    width: \"205px\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                p: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    sx: {\n                      color: \"rgba(95, 158, 160, 1)\"\n                    },\n                    children: translate(\"Image Properties\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 844,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleCloseSettingsPopover,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: CrossIcon\n                      },\n                      style: {\n                        color: \"black\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 854,\n                      columnNumber: 12\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 850,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 839,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate(\"Coming soon\"),\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    mt: 2,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      sx: {\n                        marginBottom: \"10px\"\n                      },\n                      children: translate(\"Image Actions\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 862,\n                      columnNumber: 11\n                    }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                      select: true,\n                      fullWidth: true,\n                      variant: \"outlined\",\n                      size: \"small\",\n                      value: selectedAction,\n                      onChange: handleActionChange,\n                      sx: {\n                        \"& .MuiOutlinedInput-root\": {\n                          borderColor: \"rgba(246, 238, 238, 1)\"\n                        }\n                      },\n                      disabled: true,\n                      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"none\",\n                        children: translate(\"None\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 883,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"specificStep\",\n                        children: translate(\"Specific Step\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 884,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"openUrl\",\n                        children: translate(\"Open URL\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 885,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"clickElement\",\n                        children: translate(\"Click Element\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 886,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startTour\",\n                        children: translate(\"Start Tour\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 887,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startMicroSurvey\",\n                        children: translate(\"Start Micro Survey\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 888,\n                        columnNumber: 14\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 869,\n                      columnNumber: 11\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 10\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: translate(\"Image Formatting\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 893,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    gap: 1,\n                    mt: 1,\n                    children: [\"Fill\", \"Fit\"].map(item => {\n                      // Get current image's objectFit to determine selected state\n                      const currentContainer = imagesContainer.find(c => c.id === imageAnchorEl.containerId);\n                      const currentImage = currentContainer === null || currentContainer === void 0 ? void 0 : currentContainer.images.find(img => img.id === imageAnchorEl.buttonId);\n                      const currentObjectFit = (currentImage === null || currentImage === void 0 ? void 0 : currentImage.objectFit) || IMG_OBJECT_FIT;\n\n                      // Determine if this button should be selected\n                      const isSelected = item === \"Fill\" && currentObjectFit === \"cover\" || item === \"Fit\" && currentObjectFit === \"contain\";\n                      return /*#__PURE__*/_jsxDEV(Button, {\n                        onClick: () => toggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item),\n                        variant: \"outlined\",\n                        size: \"small\",\n                        sx: {\n                          width: \"88.5px\",\n                          height: \"41px\",\n                          padding: \"10px 12px\",\n                          gap: \"12px\",\n                          borderRadius: \"6px 6px 6px 6px\",\n                          border: isSelected ? \"1px solid rgba(95, 158, 160, 1)\" : \"1px solid rgba(246, 238, 238, 1)\",\n                          backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\n                          backgroundBlendMode: \"multiply\",\n                          color: \"black\",\n                          \"&:hover\": {\n                            backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\"\n                          }\n                        },\n                        children: translate(item)\n                      }, item, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 915,\n                        columnNumber: 14\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 899,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 838,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 807,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Background Color\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleBackgroundColorClick,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  backgroundColor: selectedColor,\n                  borderRadius: \"100%\",\n                  width: \"20px\",\n                  height: \"20px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 954,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 953,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 952,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\") : translate(\"Clone Section\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleCloneImgContainer,\n              size: \"small\",\n              disabled: isCloneDisabled,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 975,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 970,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 969,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 968,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Delete Section\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleDeleteSection,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 990,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 986,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 983,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 736,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 5\n    }, this) : null, isModelOpen && /*#__PURE__*/_jsxDEV(SelectImageFromApplication, {\n      isOpen: isModelOpen,\n      handleModelClose: () => setModelOpen(false),\n      onImageSelect: handleImageUploadFormApp,\n      setFormOfUpload: setFormOfUpload,\n      formOfUpload: formOfUpload,\n      handleReplaceImage: handleReplaceImage,\n      isReplaceImage: isReplaceImage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 999,\n      columnNumber: 6\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      open: colorPickerOpen,\n      anchorEl: colorPickerAnchorEl,\n      onClose: handleCloseColorPicker,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(ChromePicker, {\n          color: currentContainerColor,\n          onChange: handleColorChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1016,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1020,\n          columnNumber: 8\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1015,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1002,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(ImageSection, \"phKmd8/vYJ2if4XJ7NWyWxDuMKo=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ImageSection;\nexport default ImageSection;\nvar _c;\n$RefreshReg$(_c, \"ImageSection\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "Typography", "Popover", "IconButton", "TextField", "MenuItem", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "RemoveIcon", "AddIcon", "useTranslation", "uploadfile", "hyperlink", "files", "uploadicon", "replaceimageicon", "copyicon", "deleteicon", "sectionheight", "Settings", "CrossIcon", "useDrawerStore", "IMG_CONTAINER_DEFAULT_HEIGHT", "IMG_CONTAINER_MAX_HEIGHT", "IMG_CONTAINER_MIN_HEIGHT", "IMG_OBJECT_FIT", "IMG_STEP_VALUE", "ChromePicker", "SelectImageFromApplication", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageSection", "setImageSrc", "setImageName", "onDelete", "onClone", "isCloneDisabled", "_s", "_imagesContainer$find", "t", "translate", "uploadImage", "imagesContainer", "imageAnchorEl", "setImageAnchorEl", "replaceImage", "cloneImageContainer", "deleteImageContainer", "updateImageContainer", "toggleFit", "storeImageSrc", "state", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "snackbarKey", "setSnackbarKey", "openSnackbar", "prev", "closeSnackbar", "showHyperlinkInput", "setShowHyperlinkInput", "currentContainerId", "isOpen", "imageLink", "setImageLink", "colorPickerAnchorEl", "setColorPickerAnchorEl", "currentImageSectionInfo", "setCurrentImageSectionInfo", "isImage", "height", "selectedAction", "setSelectedAction", "isModelOpen", "setModelOpen", "formOfUpload", "setFormOfUpload", "settingsAnchorEl", "setSettingsAnchorEl", "selectedColor", "setSelectedColor", "isReplaceImage", "setReplaceImage", "guidePopupRef", "popoverPositions", "setPopoverPositions", "imagePopover", "settingsPopover", "openSettingsPopover", "Boolean", "handleActionChange", "event", "target", "value", "handleSettingsClick", "currentTarget", "handleCloseSettingsPopover", "imageContainerStyle", "width", "display", "justifyContent", "alignItems", "padding", "margin", "overflow", "imageStyle", "borderRadius", "iconRowStyle", "gap", "marginTop", "iconTextStyle", "flexDirection", "handleImageUpload", "_event$target$files", "file", "_event$target$files2", "parts", "name", "split", "extension", "pop", "length", "reader", "FileReader", "onloadend", "base64Image", "result", "containerId", "altText", "id", "crypto", "randomUUID", "url", "backgroundColor", "objectFit", "readAsDataURL", "handleImageUploadFormApp", "Url", "buttonId", "FileName", "handleReplaceImage", "_event$target$files3", "handleClick", "imageId", "currentHeight", "includes", "handleClose", "open", "colorPickerOpen", "undefined", "handleIncreaseHeight", "prevHeight", "newHeight", "Math", "min", "handleDecreaseHeight", "max", "triggerImageUpload", "_document$getElementB", "document", "getElementById", "click", "currentContainerColor", "find", "item", "style", "handleDeleteSection", "handleLinkSubmit", "key", "handleCloneImgContainer", "handleCloseColorPicker", "handleColorChange", "color", "hex", "handleBackgroundColorClick", "getGuidePopupPosition", "element", "querySelector", "rect", "getBoundingClientRect", "top", "left", "getImagePopoverPosition", "guidePopupPos", "popoverHeight", "requiredGap", "minTopMargin", "viewportHeight", "window", "innerHeight", "popoverWidth", "viewportWidth", "innerWidth", "positionWithGap", "bannerElement", "wouldCollideWithBanner", "bannerBottom", "bottom", "borderAndSafetyMargin", "minClearanceFromBanner", "topPosition", "isPositionedOnTop", "availableSpaceAbove", "gaps", "g", "maxTopPosition", "leftPosition", "position", "zIndex", "getSettingsPopoverPosition", "settingsPopupHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatePopoverPositions", "handlePositionUpdate", "positionCheckInterval", "setInterval", "clearInterval", "qadptPositionCheckInterval", "addEventListener", "perfectScrollbarElement", "elementsToObserve", "filter", "resizeObserver", "mutationObserver", "ResizeObserver", "entries", "hasSignificantChange", "some", "entry", "contentRect", "lastWidth", "parseFloat", "dataset", "lastHeight", "abs", "toString", "setTimeout", "for<PERSON>ach", "observe", "guidePopupElement", "MutationObserver", "mutations", "shouldUpdate", "mutation", "type", "attributeName", "childList", "subtree", "attributes", "attributeFilter", "_resizeObserver", "_mutationObserver", "removeEventListener", "intervalId", "disconnect", "children", "map", "_item$images$", "_item$images$2", "_item$images$3", "_item$style", "imageSrc", "images", "currentSecHeight", "sx", "onClick", "e", "component", "onMouseOver", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textAlign", "dangerouslySetInnerHTML", "__html", "variant", "align", "fontSize", "fontWeight", "onChange", "onKeyDown", "autoFocus", "title", "pointerEvents", "cursor", "opacity", "className", "_document$getElementB2", "stopPropagation", "accept", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "anchorEl", "anchorReference", "slotProps", "paper", "marginLeft", "marginRight", "size", "disabled", "p", "mt", "marginBottom", "select", "fullWidth", "borderColor", "currentC<PERSON><PERSON>", "c", "currentImage", "img", "currentObjectFit", "isSelected", "border", "backgroundBlendMode", "handleModelClose", "onImageSelect", "transform<PERSON><PERSON>in", "_c", "$RefreshReg$"], "sources": ["E:/Code/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/Imagesection.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport {\r\n\t<PERSON>,\r\n\tTypo<PERSON>,\r\n\tPopover,\r\n\tIconButton,\r\n\tTextField,\r\n\tMenuItem,\r\n\tButton,\r\n\tTooltip,\r\n\tSnackbar,\r\n\tAlert\r\n} from \"@mui/material\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport { FileUpload } from \"../../../models/FileUpload\";\r\n\r\nimport {\r\n\tuploadfile,\r\n\thyperlink,\r\n\tfiles,\r\n\tuploadicon,\r\n\treplaceimageicon,\r\n\tcopyicon,\r\n\tdeleteicon,\r\n\tsectionheight,\r\n\tSettings,\r\n\tCrossIcon,\r\n} from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, {\r\n\tIMG_CONTAINER_DEFAULT_HEIGHT,\r\n\tIMG_CONTAINER_MAX_HEIGHT,\r\n\tIMG_CONTAINER_MIN_HEIGHT,\r\n\tIMG_OBJECT_FIT,\r\n\tIMG_STEP_VALUE,\r\n} from \"../../../store/drawerStore\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport \"./PopupSections.css\";\r\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\r\n\r\nconst ImageSection = ({ setImageSrc, setImageName, onDelete, onClone, isCloneDisabled }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tuploadImage,\r\n\t\timagesContainer,\r\n\t\timageAnchorEl,\r\n\t\tsetImageAnchorEl,\r\n\t\treplaceImage,\r\n\t\tcloneImageContainer,\r\n\t\tdeleteImageContainer,\r\n\t\tupdateImageContainer,\r\n\t\ttoggleFit,\r\n\t\tsetImageSrc: storeImageSrc,\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\tconst [snackbarMessage, setSnackbarMessage] = useState('');\r\n\tconst [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('info');\r\n\r\n\tconst [snackbarKey, setSnackbarKey] = useState<number>(0); \r\n\r\n\tconst openSnackbar = () => {\r\n\t\tsetSnackbarKey(prev => prev + 1);\r\n\t\tsetSnackbarOpen(true);\r\n\t};\r\n\tconst closeSnackbar = () => {\r\n\t\tsetSnackbarOpen(false);\r\n\t};\r\n\tconst [showHyperlinkInput, setShowHyperlinkInput] = useState<{ currentContainerId: string; isOpen: boolean }>({\r\n\t\tcurrentContainerId: \"\",\r\n\t\tisOpen: false,\r\n\t});\r\n\tconst [imageLink, setImageLink] = useState<string>(\"\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [currentImageSectionInfo, setCurrentImageSectionInfo] = useState<{\r\n\t\tcurrentContainerId: string;\r\n\t\tisImage: boolean;\r\n\t\theight: number;\r\n\t}>({ currentContainerId: \"\", isImage: false, height: IMG_CONTAINER_DEFAULT_HEIGHT });\r\n\r\n\tconst [selectedAction, setSelectedAction] = useState(\"none\");\r\n\tconst [isModelOpen, setModelOpen] = useState(false);\r\n\tconst [formOfUpload, setFormOfUpload] = useState<String>(\"\");\r\n\tconst [settingsAnchorEl, setSettingsAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [selectedColor, setSelectedColor] = useState<string>(\"#313030\");\r\n\tconst [isReplaceImage, setReplaceImage] = useState(false);\r\n\tconst guidePopupRef = useRef<HTMLElement | null>(null);\r\n\tconst [popoverPositions, setPopoverPositions] = useState({\r\n\t\timagePopover: {},\r\n\t\tsettingsPopover: {}\r\n\t});\r\n\r\n\r\n\tconst openSettingsPopover = Boolean(settingsAnchorEl);\r\n\r\n\tconst handleActionChange = (event: any) => {\r\n\t\tsetSelectedAction(event.target.value);\r\n\t};\r\n\r\n\tconst handleSettingsClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSettingsAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleCloseSettingsPopover = () => {\r\n\t\tsetSettingsAnchorEl(null);\r\n\t};\r\n\tconst imageContainerStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\talignItems: \"center\",\r\n\t\tpadding: 0,\r\n\t\tmargin: 0,\r\n\t\toverflow: \"hidden\",\r\n\t};\r\n\r\n\tconst imageStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tmargin: 0,\r\n\t\tpadding: 0,\r\n\t\tborderRadius: \"0\",\r\n\t};\r\n\r\n\tconst iconRowStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tgap: \"16px\",\r\n\t\tmarginTop: \"10px\",\r\n\t};\r\n\r\n\tconst iconTextStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\twidth: \"100%\",\r\n\t};\r\n\r\n\tconst handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst parts = file.name.split('.');\r\n   \t\t\tconst extension = parts.pop();\r\n\r\n   \t\t // Check for double extensions (e.g. file.html.png) or missing/invalid extension\r\n   \t\t\t if (parts.length > 1 || !extension ) {\r\n\t\t\t  setSnackbarMessage(\"Uploaded file name should not contain any special character\");\r\n       \t\t setSnackbarSeverity(\"error\");\r\n\t\t\t setSnackbarOpen(true);\r\n\t\t\t event.target.value = '';\r\n      \t\t return;\r\n\t\t\t \r\n   \t\t\t }\r\n\t\t\t if(file.name.length > 128){\r\n\t\t\t\tsetSnackbarMessage(\"File name should not exceed 128 characters\");\r\n       \t\t\tsetSnackbarSeverity(\"error\");\r\n\t\t\t \tsetSnackbarOpen(true);\r\n\t\t\t \tevent.target.value = '';\r\n      \t\t \treturn;\r\n\t\t\t }\r\n\t\t\tsetImageName(event.target.files?.[0].name);\r\n\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\tconst base64Image = reader.result as string;\r\n\t\t\t\tstoreImageSrc(base64Image);\r\n\t\t\t\tsetImageSrc(base64Image);\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\turl: base64Image,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\r\n\tconst handleImageUploadFormApp = (file: FileUpload) => {\r\n\t\tif (file) {\r\n\t\t\tstoreImageSrc(file.Url);\r\n\t\t\tsetImageSrc(file.Url);\r\n\t\t\tif (isReplaceImage) {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: file.Url,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t\tsetReplaceImage(false);\r\n\t\t\t} else {\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: crypto.randomUUID(), // Use existing ID\r\n\t\t\t\t\turl: file.Url, // Directly use the URL\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\tconst handleReplaceImage = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: reader.result,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClick = (\r\n\t\tevent: React.MouseEvent<HTMLElement>,\r\n\t\tcontainerId: string,\r\n\t\timageId: string,\r\n\t\tisImage: boolean,\r\n\t\tcurrentHeight: number\r\n\t) => {\r\n\t\t// @ts-ignore\r\n\t\tif ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: imageId,\r\n\t\t\tcontainerId: containerId,\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: event.currentTarget,\r\n\t\t});\r\n\t\tsetSettingsAnchorEl(null);\r\n\t\tsetCurrentImageSectionInfo({\r\n\t\t\tcurrentContainerId: containerId,\r\n\t\t\tisImage,\r\n\t\t\theight: currentHeight,\r\n\t\t});\r\n\t\tsetShowHyperlinkInput({\r\n\t\t\tcurrentContainerId: \"\",\r\n\t\t\tisOpen: false,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t};\r\n\r\n\tconst open = Boolean(imageAnchorEl.value);\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\r\n\tconst id = open ? \"image-popover\" : undefined;\r\n\r\n\tconst handleIncreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\r\n\t\tconst newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst handleDecreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\r\n\t\tconst newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst triggerImageUpload = () => {\r\n\t\tdocument.getElementById(\"replace-upload\")?.click();\r\n\t\t// setModelOpen(true);\r\n\t\t// setReplaceImage(true);\r\n\t};\r\n\r\n\tconst currentContainerColor =\r\n\t\timagesContainer.find((item) => item.id === imageAnchorEl.containerId)?.style.backgroundColor || \"transparent\";\r\n\t// Function to delete the section\r\n\tconst handleDeleteSection = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\r\n\t\t// Call the delete function from the store\r\n\t\tdeleteImageContainer(imageAnchorEl.containerId);\r\n\r\n\t\t// Call the onDelete callback if provided\r\n\t\tif (onDelete) {\r\n\t\t\tonDelete();\r\n\t\t}\r\n\t};\r\n\r\n\r\n\tconst handleLinkSubmit = (event: React.KeyboardEvent<HTMLInputElement>) => {\r\n\t\tif (event.key === \"Enter\" && imageLink) {\r\n\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\taltText: \"New Image\",\r\n\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\turl: imageLink,\r\n\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t});\r\n\t\t\tsetShowHyperlinkInput({\r\n\t\t\t\tcurrentContainerId: \"\",\r\n\t\t\t\tisOpen: false,\r\n\t\t\t});\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloneImgContainer = () => {\r\n\t\t// Check if cloning is disabled due to section limits\r\n\t\tif (isCloneDisabled) {\r\n\t\t\treturn; // Don't clone if limit is reached\r\n\t\t}\r\n\r\n\t\t// Call the clone function from the store\r\n\t\tcloneImageContainer(imageAnchorEl.containerId);\r\n\r\n\t\t// Call the onClone callback if provided\r\n\t\tif (onClone) {\r\n\t\t\tonClone();\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\tsetSelectedColor(color.hex);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\t\r\n\tconst getGuidePopupPosition = () => {\r\n\t\tconst element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') ||\r\n\t\t\t\t\t\tdocument.getElementById('guide-popup');\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top,\r\n\t\t\t\tleft: rect.left,\r\n\t\t\t\twidth: rect.width,\r\n\t\t\t\theight: rect.height\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\tconst getImagePopoverPosition = () => {\r\n\t\tconst guidePopupPos = getGuidePopupPosition();\r\n\t\tif (!guidePopupPos) return {};\r\n\r\n\t\tconst popoverHeight = 40;\r\n\t\tconst requiredGap = 10;\r\n\t\tconst minTopMargin = 8;\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\t\tconst popoverWidth = 500;\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t// Calculate preferred position above GuidePopup\r\n\t\tconst positionWithGap = guidePopupPos.top - popoverHeight - requiredGap;\r\n\r\n\t\t// Get banner element and its position for collision detection\r\n\t\tconst bannerElement = document.querySelector('.qadpt-ext-banner') as HTMLElement;\r\n\t\tlet wouldCollideWithBanner = false;\r\n\r\n\t\tif (bannerElement) {\r\n\t\t\tconst bannerBottom = bannerElement.getBoundingClientRect().bottom;\r\n\t\t\t// Account for GuidePopup border thickness (5px-10px) and add small safety margin\r\n\t\t\tconst borderAndSafetyMargin = 15; // 10px max border + 5px safety margin\r\n\t\t\tconst minClearanceFromBanner = bannerBottom + borderAndSafetyMargin;\r\n\r\n\t\t\t// Only consider collision if the popover would actually overlap with banner area\r\n\t\t\twouldCollideWithBanner = positionWithGap < minClearanceFromBanner;\r\n\t\t}\r\n\r\n\t\tlet topPosition;\r\n\t\tlet isPositionedOnTop = false; // Track if we're using fallback positioning\r\n\r\n\t\tif (wouldCollideWithBanner) {\r\n\t\t\t// Fallback: Position on top of GuidePopup instead of above it\r\n\t\t\ttopPosition = guidePopupPos.top + requiredGap;\r\n\t\t\tisPositionedOnTop = true;\r\n\t\t} else if (positionWithGap >= minTopMargin) {\r\n\t\t\t// Standard positioning above GuidePopup\r\n\t\t\ttopPosition = positionWithGap;\r\n\t\t} else {\r\n\t\t\t// Limited space above - use progressive gap reduction\r\n\t\t\tconst availableSpaceAbove = guidePopupPos.top - popoverHeight - minTopMargin;\r\n\t\t\tconst gaps = [15, 10, 5, 2];\r\n\t\t\tconst gap = gaps.find(g => availableSpaceAbove >= g) || 2;\r\n\t\t\ttopPosition = gap === 2\r\n\t\t\t\t? Math.max(minTopMargin, guidePopupPos.top - popoverHeight - 2)\r\n\t\t\t\t: guidePopupPos.top - popoverHeight - gap;\r\n\t\t}\r\n\r\n\t\t// Viewport boundary checks\r\n\t\tconst maxTopPosition = viewportHeight - popoverHeight;\r\n\t\ttopPosition = Math.min(topPosition, maxTopPosition);\r\n\r\n\t\t// Ensure we don't go below the minimum top margin (except for on-top positioning)\r\n\t\tif (!isPositionedOnTop) {\r\n\t\t\ttopPosition = Math.max(topPosition, minTopMargin);\r\n\t\t}\r\n\r\n\t\t// Calculate horizontal position - center relative to GuidePopUp\r\n\t\tlet leftPosition = guidePopupPos.left + (guidePopupPos.width / 2) - 250;\r\n\t\tleftPosition = Math.max(10, Math.min(leftPosition, viewportWidth - popoverWidth - 10));\r\n\r\n\t\treturn {\r\n\t\t\ttop: topPosition,\r\n\t\t\tleft: leftPosition,\r\n\t\t\tposition: 'fixed' as const,\r\n\t\t\tzIndex: 999999\r\n\t\t};\r\n\t};\r\n\tconst getSettingsPopoverPosition = () => {\r\n\t\tconst guidePopupPos = getGuidePopupPosition();\r\n\t\tif (!guidePopupPos) return {};\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\t\tconst settingsPopupHeight = 200; \r\n\t\tconst settingsPopupWidth = 300; \r\n\t\tlet leftPosition = guidePopupPos.left + guidePopupPos.width + 10; \r\n\t\tlet topPosition = guidePopupPos.top + (guidePopupPos.height / 2) - (settingsPopupHeight/2) + 10;\r\n\t\tif (leftPosition + settingsPopupWidth > viewportWidth - 10) {\r\n\t\t\tleftPosition = guidePopupPos.left - settingsPopupWidth - 10; // Position to the left instead\r\n\t\t}\r\n\t\ttopPosition = Math.max(10, Math.min(topPosition, viewportHeight - settingsPopupHeight - 10));\r\n\t\tleftPosition = Math.max(10, Math.min(leftPosition, viewportWidth - settingsPopupWidth - 10));\r\n\t\treturn {\r\n\t\t\ttop: topPosition,\r\n\t\t\tleft: leftPosition,\r\n\t\t\tposition: 'fixed' as const,\r\n\t\t\tzIndex: 999999\r\n\t\t};\r\n\t};\r\n\tconst updatePopoverPositions = () => {\r\n\t\tsetPopoverPositions({\r\n\t\t\timagePopover: getImagePopoverPosition(),\r\n\t\t\tsettingsPopover: getSettingsPopoverPosition()\r\n\t\t});\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tconst handlePositionUpdate = () => updatePopoverPositions();\r\n\t\tif (open || openSettingsPopover) {\r\n\t\t\tupdatePopoverPositions();\r\n\t\t\tconst positionCheckInterval = setInterval(() => {\r\n\t\t\t\tif (open || openSettingsPopover) {\r\n\t\t\t\t\tupdatePopoverPositions();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tclearInterval(positionCheckInterval);\r\n\t\t\t\t}\r\n\t\t\t}, 200); \r\n\t\t\t(window as any).qadptPositionCheckInterval = positionCheckInterval;\r\n\t\t}\r\n\t\twindow.addEventListener('resize', handlePositionUpdate);\r\n\t\twindow.addEventListener('scroll', handlePositionUpdate);\r\n\t\tconst perfectScrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\r\n\t\tif (perfectScrollbarElement) {\r\n\t\t\tperfectScrollbarElement.addEventListener('ps-scroll-y', handlePositionUpdate);\r\n\t\t\tperfectScrollbarElement.addEventListener('scroll', handlePositionUpdate);\r\n\t\t}\r\n\t\tconst elementsToObserve = [\r\n\t\t\tdocument.getElementById('guide-popup'),\r\n\t\t\tdocument.querySelector('.qadpt-guide-popup .MuiDialog-paper'),\r\n\t\t\tdocument.querySelector('.qadpt-guide-popup .ps')\r\n\t\t].filter(Boolean) as HTMLElement[];\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\t\tif (window.ResizeObserver && elementsToObserve.length > 0) {\r\n\t\t\tresizeObserver = new ResizeObserver((entries) => {\r\n\t\t\t\tconst hasSignificantChange = entries.some((entry) => {\r\n\t\t\t\t\tconst { width, height } = entry.contentRect;\r\n\t\t\t\t\tconst element = entry.target as HTMLElement;\r\n\t\t\t\t\tconst lastWidth = parseFloat(element.dataset.lastWidth || '0');\r\n\t\t\t\t\tconst lastHeight = parseFloat(element.dataset.lastHeight || '0');\r\n\t\t\t\t\tif (Math.abs(width - lastWidth) > 1 || Math.abs(height - lastHeight) > 1) {\r\n\t\t\t\t\t\telement.dataset.lastWidth = width.toString();\r\n\t\t\t\t\t\telement.dataset.lastHeight = height.toString();\r\n\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t});\r\n\t\t\t\tif (hasSignificantChange) {\r\n\t\t\t\t\tupdatePopoverPositions();\r\n\t\t\t\t\tsetTimeout(updatePopoverPositions, 50);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\telementsToObserve.forEach(element => resizeObserver!.observe(element));\r\n\t\t}\r\n\t\tconst guidePopupElement = elementsToObserve[0]; \r\n\t\tif (guidePopupElement && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver((mutations) => {\r\n\t\t\t\tconst shouldUpdate = mutations.some((mutation) => {\r\n\t\t\t\t\treturn mutation.type === 'childList' ||\r\n\t\t\t\t\t\t(mutation.type === 'attributes' &&\r\n\t\t\t\t\t\t ['style', 'class'].includes(mutation.attributeName || ''));\r\n\t\t\t\t});\r\n\t\t\t\tif (shouldUpdate) {\r\n\t\t\t\t\tsetTimeout(updatePopoverPositions, 50); \r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(guidePopupElement, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\t\treturn () => {\r\n\t\t\twindow.removeEventListener('resize', handlePositionUpdate);\r\n\t\t\twindow.removeEventListener('scroll', handlePositionUpdate);\r\n\t\t\tconst perfectScrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\r\n\t\t\tif (perfectScrollbarElement) {\r\n\t\t\t\tperfectScrollbarElement.removeEventListener('ps-scroll-y', handlePositionUpdate);\r\n\t\t\t\tperfectScrollbarElement.removeEventListener('scroll', handlePositionUpdate);\r\n\t\t\t}\r\n\t\t\tconst intervalId = (window as any).qadptPositionCheckInterval;\r\n\t\t\tif (intervalId) {\r\n\t\t\t\tclearInterval(intervalId);\r\n\t\t\t\tdelete (window as any).qadptPositionCheckInterval;\r\n\t\t\t}\r\n\t\t\tresizeObserver?.disconnect();\r\n\t\t\tmutationObserver?.disconnect();\r\n\t\t};\r\n\t}, [open, openSettingsPopover]);\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{imagesContainer.map((item) => {\r\n\t\t\t\tconst imageSrc = item.images[0]?.url;\r\n\t\t\t\tconst imageId = item.images[0]?.id;\r\n\t\t\t\tconst objectFit = item.images[0]?.objectFit || IMG_OBJECT_FIT;\r\n\t\t\t\tconst currentSecHeight = (item?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT;\r\n\t\t\t\tconst id = item.id;\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tkey={id}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tmargin: \"0px\",\r\n\t\t\t\t\t\t\toverflow: \"auto\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t...imageContainerStyle,\r\n\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor,\r\n\t\t\t\t\t\t\t\theight: `${item.style.height}px`,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={(e) => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight)}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid={id}\r\n\t\t\t\t\t\t\tonMouseOver={() => {\r\n\t\t\t\t\t\t\t\tsetImageAnchorEl({\r\n\t\t\t\t\t\t\t\t\tbuttonId: imageId,\r\n\t\t\t\t\t\t\t\t\tcontainerId: id,\r\n\t\t\t\t\t\t\t\t\tvalue: null,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{imageSrc ? (\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={imageSrc}\r\n\t\t\t\t\t\t\t\t\talt=\"Uploaded\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ ...imageStyle, objectFit }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tsx={iconTextStyle}\r\n\t\t\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadfile }}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"inline-block\" }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"h6\"\r\n\t\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\", fontWeight: \"600\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Upload file\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Drag & Drop to upload file\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ marginTop: \"8px\", fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Or\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t{showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? (\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={imageLink}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setImageLink(e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tonKeyDown={handleLinkSubmit}\r\n\t\t\t\t\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={iconRowStyle}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n  <div style={{ pointerEvents: \"auto\", cursor:\"pointer\"}}>\r\n    <span\r\n      dangerouslySetInnerHTML={{ __html: hyperlink }}\r\n      style={{\r\n        color: \"black\",\r\n        cursor: \"pointer\",\r\n        fontSize: \"32px\",\r\n        opacity: \"0.5\",\r\n        pointerEvents: \"none\",\r\n      }}\r\n      id=\"hyperlink\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n    />\r\n  </div>\r\n</Tooltip>\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t//setModelOpen(true);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: files }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\", opacity: \"0.5\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"folder\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t//title=\"Coming Soon\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Upload File\")}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tevent?.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdocument.getElementById(\"file-upload\")?.click();\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload1\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleImageUpload}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={closeSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Alert onClose={closeSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{snackbarMessage}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Alert>\r\n\t\t\t\t\t\t\t\t\t\t\t</Snackbar>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t);\r\n\t\t\t})}\r\n\t\t\t{Boolean(imageAnchorEl.value) ? (\r\n\t\t\t\t<Popover\r\n\t\t\t\t\tclassName=\"qadpt-imgsec-popover\"\r\n\t\t\t\t\tid={id}\r\n\t\t\t\t\topen={open}\r\n\t\t\t\t\tanchorEl={null}\r\n\t\t\t\t\tonClose={handleClose}\r\n\t\t\t\t\tanchorReference=\"none\"\r\n\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\t\t...popoverPositions.imagePopover,\r\n\t\t\t\t\t\t\t\theight: 'auto',\r\n\t\t\t\t\t\t\t\twidth: 'auto',\r\n\t\t\t\t\t\t\t\tpadding: '5px 10px',\r\n\t\t\t\t\t\t\t\tmarginLeft: 'auto',\r\n\t\t\t\t\t\t\t\tmarginRight: 'auto',\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tgap: \"15px\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tpadding: \"0 10px\",\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box sx={{ display: \"flex\" }}>\r\n\t\t\t\t\t\t\t{currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId &&\r\n\t\t\t\t\t\t\tcurrentImageSectionInfo.isImage ? (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tfontSize=\"12px\"\r\n\t\t\t\t\t\t\t\t\t\tmarginLeft={\"5px\"}\r\n\t\t\t\t\t\t\t\t\t\tonClick={triggerImageUpload}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Replace Image\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\tid=\"replace-upload\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleReplaceImage}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t</Box>\r\n\r\n<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-tool-items\"\r\n\t\t\t\t\t\t\tsx={{ display: \"flex\", alignItems: \"center\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: sectionheight }}\r\n\t\t\t\t\t\t\tstyle={{ display: \"flex\" }}/>\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\") : translate(\"Decrease height\")}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleDecreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<RemoveIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t<Typography fontSize=\"12px\">{currentImageSectionInfo.height}</Typography>\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\") : translate(\"Increase height\")}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleIncreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<AddIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Settings\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={handleSettingsClick}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Settings }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\"}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-imgset\"\r\n\t\t\t\t\t\t\t\topen={openSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorEl={null}\r\n\t\t\t\t\t\t\t\tonClose={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorReference=\"none\"\r\n\t\t\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\t\t\t\t\t...popoverPositions.settingsPopover,\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"205px\",\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box p={2}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"subtitle1\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ color: \"rgba(95, 158, 160, 1)\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Properties\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: CrossIcon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{ marginBottom: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Actions\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tselect\r\n\t\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={selectedAction}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleActionChange}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"none\">{translate(\"None\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"specificStep\">{translate(\"Specific Step\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"openUrl\">{translate(\"Open URL\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"clickElement\">{translate(\"Click Element\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startTour\">{translate(\"Start Tour\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startMicroSurvey\">{translate(\"Start Micro Survey\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t</TextField>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Formatting\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\tgap={1}\r\n\t\t\t\t\t\t\t\t\t\t\tmt={1}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{[\"Fill\", \"Fit\"].map((item) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Get current image's objectFit to determine selected state\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentContainer = imagesContainer.find((c) => c.id === imageAnchorEl.containerId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentImage = currentContainer?.images.find((img) => img.id === imageAnchorEl.buttonId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentObjectFit = currentImage?.objectFit || IMG_OBJECT_FIT;\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Determine if this button should be selected\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst isSelected = (item === \"Fill\" && currentObjectFit === \"cover\") ||\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  (item === \"Fit\" && currentObjectFit === \"contain\");\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttoggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item as \"Fit\" | \"Fill\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"88.5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"41px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"10px 12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px 6px 6px 6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"1px solid rgba(95, 158, 160, 1)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"1px solid rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundBlendMode: \"multiply\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(item)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Background Color\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: selectedColor,\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"100%\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t}} />\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\") : translate(\"Clone Section\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleCloneImgContainer}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Delete Section\")}>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleDeleteSection}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Popover>\r\n\t\t\t) : null}\r\n\t\t\t{\r\n\t\t\t\tisModelOpen && (\r\n\t\t\t\t\t<SelectImageFromApplication isOpen={isModelOpen} handleModelClose={() => setModelOpen(false)} onImageSelect={handleImageUploadFormApp} setFormOfUpload={setFormOfUpload} formOfUpload={formOfUpload} handleReplaceImage={handleReplaceImage} isReplaceImage={isReplaceImage}/>\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t\t<Popover\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={currentContainerColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<style>\r\n    {`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n  </style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ImageSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACCC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,KAAK,QACC,eAAe;AACtB,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,SAASC,cAAc,QAAQ,eAAe;AAI9C,SACCC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,QAAQ,EACRC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,SAAS,QACH,6BAA6B;AACpC,OAAOC,cAAc,IACpBC,4BAA4B,EAC5BC,wBAAwB,EACxBC,wBAAwB,EACxBC,cAAc,EACdC,cAAc,QACR,4BAA4B;AACnC,SAASC,YAAY,QAAqB,aAAa;AACvD,OAAO,qBAAqB;AAC5B,OAAOC,0BAA0B,MAAM,yCAAyC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjF,MAAMC,YAAY,GAAGA,CAAC;EAAEC,WAAW;EAAEC,YAAY;EAAEC,QAAQ;EAAEC,OAAO;EAAEC;AAAqB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChG,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGhC,cAAc,CAAC,CAAC;EACzC,MAAM;IACLiC,WAAW;IACXC,eAAe;IACfC,aAAa;IACbC,gBAAgB;IAChBC,YAAY;IACZC,mBAAmB;IACnBC,oBAAoB;IACpBC,oBAAoB;IACpBC,SAAS;IACTjB,WAAW,EAAEkB;EACd,CAAC,GAAG/B,cAAc,CAAEgC,KAAK,IAAKA,KAAK,CAAC;EACpC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC8D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAA2C,MAAM,CAAC;EAE1G,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAS,CAAC,CAAC;EAEzD,MAAMkE,YAAY,GAAGA,CAAA,KAAM;IAC1BD,cAAc,CAACE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAChCR,eAAe,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC3BT,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD,MAAM,CAACU,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtE,QAAQ,CAAkD;IAC7GuE,kBAAkB,EAAE,EAAE;IACtBC,MAAM,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAAC2E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5E,QAAQ,CAAqB,IAAI,CAAC;EACxF,MAAM,CAAC6E,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG9E,QAAQ,CAInE;IAAEuE,kBAAkB,EAAE,EAAE;IAAEQ,OAAO,EAAE,KAAK;IAAEC,MAAM,EAAEtD;EAA6B,CAAC,CAAC;EAEpF,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlF,QAAQ,CAAC,MAAM,CAAC;EAC5D,MAAM,CAACmF,WAAW,EAAEC,YAAY,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqF,YAAY,EAAEC,eAAe,CAAC,GAAGtF,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACuF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxF,QAAQ,CAAqB,IAAI,CAAC;EAClF,MAAM,CAACyF,aAAa,EAAEC,gBAAgB,CAAC,GAAG1F,QAAQ,CAAS,SAAS,CAAC;EACrE,MAAM,CAAC2F,cAAc,EAAEC,eAAe,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM6F,aAAa,GAAG5F,MAAM,CAAqB,IAAI,CAAC;EACtD,MAAM,CAAC6F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/F,QAAQ,CAAC;IACxDgG,YAAY,EAAE,CAAC,CAAC;IAChBC,eAAe,EAAE,CAAC;EACnB,CAAC,CAAC;EAGF,MAAMC,mBAAmB,GAAGC,OAAO,CAACZ,gBAAgB,CAAC;EAErD,MAAMa,kBAAkB,GAAIC,KAAU,IAAK;IAC1CnB,iBAAiB,CAACmB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACtC,CAAC;EAED,MAAMC,mBAAmB,GAAIH,KAAoC,IAAK;IACrEb,mBAAmB,CAACa,KAAK,CAACI,aAAa,CAAC;EACzC,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACxClB,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EACD,MAAMmB,mBAAwC,GAAG;IAChDC,KAAK,EAAE,MAAM;IACb5B,MAAM,EAAE,MAAM;IACd6B,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACX,CAAC;EAED,MAAMC,UAA+B,GAAG;IACvCP,KAAK,EAAE,MAAM;IACb5B,MAAM,EAAE,MAAM;IACdiC,MAAM,EAAE,CAAC;IACTD,OAAO,EAAE,CAAC;IACVI,YAAY,EAAE;EACf,CAAC;EAED,MAAMC,YAAiC,GAAG;IACzCR,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBQ,GAAG,EAAE,MAAM;IACXC,SAAS,EAAE;EACZ,CAAC;EAED,MAAMC,aAAkC,GAAG;IAC1CX,OAAO,EAAE,MAAM;IACfY,aAAa,EAAE,QAAQ;IACvBV,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBF,KAAK,EAAE;EACR,CAAC;EAED,MAAMc,iBAAiB,GAAIrB,KAA0C,IAAK;IAAA,IAAAsB,mBAAA;IACzE,MAAMC,IAAI,IAAAD,mBAAA,GAAGtB,KAAK,CAACC,MAAM,CAACrF,KAAK,cAAA0G,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MAAA,IAAAC,oBAAA;MACT,MAAMC,KAAK,GAAGF,IAAI,CAACG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;MAC/B,MAAMC,SAAS,GAAGH,KAAK,CAACI,GAAG,CAAC,CAAC;;MAE7B;MACC,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,IAAI,CAACF,SAAS,EAAG;QACvCpE,kBAAkB,CAAC,6DAA6D,CAAC;QAC5EE,mBAAmB,CAAC,OAAO,CAAC;QAClCJ,eAAe,CAAC,IAAI,CAAC;QACrB0C,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MAEF;MACH,IAAGqB,IAAI,CAACG,IAAI,CAACI,MAAM,GAAG,GAAG,EAAC;QAC1BtE,kBAAkB,CAAC,4CAA4C,CAAC;QAC1DE,mBAAmB,CAAC,OAAO,CAAC;QACjCJ,eAAe,CAAC,IAAI,CAAC;QACrB0C,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MACN;MACDhE,YAAY,EAAAsF,oBAAA,GAACxB,KAAK,CAACC,MAAM,CAACrF,KAAK,cAAA4G,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC,CAACE,IAAI,CAAC;MAE1C,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxB,MAAMC,WAAW,GAAGH,MAAM,CAACI,MAAgB;QAC3ChF,aAAa,CAAC+E,WAAW,CAAC;QAC1BjG,WAAW,CAACiG,WAAW,CAAC;QACxBxF,WAAW,CAACE,aAAa,CAACwF,WAAW,EAAE;UACtCC,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBY,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;UACvBC,GAAG,EAAEP,WAAW;UAChBQ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEnH;QACZ,CAAC,CAAC;MACH,CAAC;MACDuG,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC;IAC3B;IACAxC,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAM8D,wBAAwB,GAAItB,IAAgB,IAAK;IACtD,IAAIA,IAAI,EAAE;MACTpE,aAAa,CAACoE,IAAI,CAACuB,GAAG,CAAC;MACvB7G,WAAW,CAACsF,IAAI,CAACuB,GAAG,CAAC;MACrB,IAAIxD,cAAc,EAAE;QACnBxC,YAAY,CAACF,aAAa,CAACwF,WAAW,EAAExF,aAAa,CAACmG,QAAQ,EAAE;UAC/DV,OAAO,EAAEd,IAAI,CAACyB,QAAQ;UACtBV,EAAE,EAAE1F,aAAa,CAACmG,QAAQ;UAC1BN,GAAG,EAAElB,IAAI,CAACuB,GAAG;UACbJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEnH;QACZ,CAAC,CAAC;QACF+D,eAAe,CAAC,KAAK,CAAC;MACvB,CAAC,MAAM;QACN7C,WAAW,CAACE,aAAa,CAACwF,WAAW,EAAE;UACtCC,OAAO,EAAEd,IAAI,CAACyB,QAAQ;UACtBV,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;UAAE;UACzBC,GAAG,EAAElB,IAAI,CAACuB,GAAG;UAAE;UACfJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEnH;QACZ,CAAC,CAAC;MACH;IACD;IACAuD,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EACD,MAAMkE,kBAAkB,GAAIjD,KAA0C,IAAK;IAAA,IAAAkD,oBAAA;IAC1E,MAAM3B,IAAI,IAAA2B,oBAAA,GAAGlD,KAAK,CAACC,MAAM,CAACrF,KAAK,cAAAsI,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC;IACpC,IAAI3B,IAAI,EAAE;MACT,MAAMQ,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxBnF,YAAY,CAACF,aAAa,CAACwF,WAAW,EAAExF,aAAa,CAACmG,QAAQ,EAAE;UAC/DV,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBY,EAAE,EAAE1F,aAAa,CAACmG,QAAQ;UAC1BN,GAAG,EAAEV,MAAM,CAACI,MAAM;UAClBO,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEnH;QACZ,CAAC,CAAC;MACH,CAAC;MACDuG,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAM4B,WAAW,GAAGA,CACnBnD,KAAoC,EACpCoC,WAAmB,EACnBgB,OAAe,EACf1E,OAAgB,EAChB2E,aAAqB,KACjB;IACJ;IACA,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACtD,KAAK,CAACC,MAAM,CAACqC,EAAE,CAAC,EAAE;IAC5DzF,gBAAgB,CAAC;MAChBkG,QAAQ,EAAEK,OAAO;MACjBhB,WAAW,EAAEA,WAAW;MACxB;MACAlC,KAAK,EAAEF,KAAK,CAACI;IACd,CAAC,CAAC;IACFjB,mBAAmB,CAAC,IAAI,CAAC;IACzBV,0BAA0B,CAAC;MAC1BP,kBAAkB,EAAEkE,WAAW;MAC/B1D,OAAO;MACPC,MAAM,EAAE0E;IACT,CAAC,CAAC;IACFpF,qBAAqB,CAAC;MACrBC,kBAAkB,EAAE,EAAE;MACtBC,MAAM,EAAE;IACT,CAAC,CAAC;EACH,CAAC;EAED,MAAMoF,WAAW,GAAGA,CAAA,KAAM;IACzB1G,gBAAgB,CAAC;MAChBkG,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,EAAE;MACf;MACAlC,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC;EAED,MAAMsD,IAAI,GAAG1D,OAAO,CAAClD,aAAa,CAACsD,KAAK,CAAC;EACzC,MAAMuD,eAAe,GAAG3D,OAAO,CAACxB,mBAAmB,CAAC;EAEpD,MAAMgE,EAAE,GAAGkB,IAAI,GAAG,eAAe,GAAGE,SAAS;EAE7C,MAAMC,oBAAoB,GAAIC,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAItI,wBAAwB,EAAE;IAC5C,MAAMuI,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,GAAGnI,cAAc,EAAEH,wBAAwB,CAAC;IACjF2B,oBAAoB,CAACL,aAAa,CAACwF,WAAW,EAAE,OAAO,EAAE;MACxDzD,MAAM,EAAEkF;IACT,CAAC,CAAC;IACFpF,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAEkF;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,MAAMG,oBAAoB,GAAIJ,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAIrI,wBAAwB,EAAE;IAC5C,MAAMsI,SAAS,GAAGC,IAAI,CAACG,GAAG,CAACL,UAAU,GAAGnI,cAAc,EAAEF,wBAAwB,CAAC;IACjF0B,oBAAoB,CAACL,aAAa,CAACwF,WAAW,EAAE,OAAO,EAAE;MACxDzD,MAAM,EAAEkF;IACT,CAAC,CAAC;IACFpF,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAEkF;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAChC,CAAAA,qBAAA,GAAAC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,cAAAF,qBAAA,uBAAzCA,qBAAA,CAA2CG,KAAK,CAAC,CAAC;IAClD;IACA;EACD,CAAC;EAED,MAAMC,qBAAqB,GAC1B,EAAAhI,qBAAA,GAAAI,eAAe,CAAC6H,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACnC,EAAE,KAAK1F,aAAa,CAACwF,WAAW,CAAC,cAAA7F,qBAAA,uBAArEA,qBAAA,CAAuEmI,KAAK,CAAChC,eAAe,KAAI,aAAa;EAC9G;EACA,MAAMiC,mBAAmB,GAAGA,CAAA,KAAM;IACjC9H,gBAAgB,CAAC;MAChBkG,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,EAAE;MACf;MACAlC,KAAK,EAAE;IACR,CAAC,CAAC;;IAEF;IACAlD,oBAAoB,CAACJ,aAAa,CAACwF,WAAW,CAAC;;IAE/C;IACA,IAAIjG,QAAQ,EAAE;MACbA,QAAQ,CAAC,CAAC;IACX;EACD,CAAC;EAGD,MAAMyI,gBAAgB,GAAI5E,KAA4C,IAAK;IAC1E,IAAIA,KAAK,CAAC6E,GAAG,KAAK,OAAO,IAAIzG,SAAS,EAAE;MACvC1B,WAAW,CAACE,aAAa,CAACwF,WAAW,EAAE;QACtCC,OAAO,EAAE,WAAW;QACpBC,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;QACvBC,GAAG,EAAErE,SAAS;QACdsE,eAAe,EAAE,aAAa;QAC9BC,SAAS,EAAEnH;MACZ,CAAC,CAAC;MACFyC,qBAAqB,CAAC;QACrBC,kBAAkB,EAAE,EAAE;QACtBC,MAAM,EAAE;MACT,CAAC,CAAC;IACH;EACD,CAAC;EAED,MAAM2G,uBAAuB,GAAGA,CAAA,KAAM;IACrC;IACA,IAAIzI,eAAe,EAAE;MACpB,OAAO,CAAC;IACT;;IAEA;IACAU,mBAAmB,CAACH,aAAa,CAACwF,WAAW,CAAC;;IAE9C;IACA,IAAIhG,OAAO,EAAE;MACZA,OAAO,CAAC,CAAC;IACV;EACD,CAAC;EAED,MAAM2I,sBAAsB,GAAGA,CAAA,KAAM;IACpCxG,sBAAsB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMyG,iBAAiB,GAAIC,KAAkB,IAAK;IACjD5F,gBAAgB,CAAC4F,KAAK,CAACC,GAAG,CAAC;IAC3BjI,oBAAoB,CAACL,aAAa,CAACwF,WAAW,EAAE,OAAO,EAAE;MACxDM,eAAe,EAAEuC,KAAK,CAACC;IACxB,CAAC,CAAC;EACH,CAAC;EAED,MAAMC,0BAA0B,GAAInF,KAAoC,IAAK;IAC5EzB,sBAAsB,CAACyB,KAAK,CAACI,aAAa,CAAC;EAC5C,CAAC;EAGD,MAAMgF,qBAAqB,GAAGA,CAAA,KAAM;IACnC,MAAMC,OAAO,GAAGjB,QAAQ,CAACkB,aAAa,CAAC,qCAAqC,CAAC,IACzElB,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IAC1C,IAAIgB,OAAO,EAAE;MACZ,MAAME,IAAI,GAAGF,OAAO,CAACG,qBAAqB,CAAC,CAAC;MAC5C,OAAO;QACNC,GAAG,EAAEF,IAAI,CAACE,GAAG;QACbC,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfnF,KAAK,EAAEgF,IAAI,CAAChF,KAAK;QACjB5B,MAAM,EAAE4G,IAAI,CAAC5G;MACd,CAAC;IACF;IACA,OAAO,IAAI;EACZ,CAAC;EACD,MAAMgH,uBAAuB,GAAGA,CAAA,KAAM;IACrC,MAAMC,aAAa,GAAGR,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACQ,aAAa,EAAE,OAAO,CAAC,CAAC;IAE7B,MAAMC,aAAa,GAAG,EAAE;IACxB,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMC,YAAY,GAAG,CAAC;IACtB,MAAMC,cAAc,GAAGC,MAAM,CAACC,WAAW;IACzC,MAAMC,YAAY,GAAG,GAAG;IACxB,MAAMC,aAAa,GAAGH,MAAM,CAACI,UAAU;;IAEvC;IACA,MAAMC,eAAe,GAAGV,aAAa,CAACH,GAAG,GAAGI,aAAa,GAAGC,WAAW;;IAEvE;IACA,MAAMS,aAAa,GAAGnC,QAAQ,CAACkB,aAAa,CAAC,mBAAmB,CAAgB;IAChF,IAAIkB,sBAAsB,GAAG,KAAK;IAElC,IAAID,aAAa,EAAE;MAClB,MAAME,YAAY,GAAGF,aAAa,CAACf,qBAAqB,CAAC,CAAC,CAACkB,MAAM;MACjE;MACA,MAAMC,qBAAqB,GAAG,EAAE,CAAC,CAAC;MAClC,MAAMC,sBAAsB,GAAGH,YAAY,GAAGE,qBAAqB;;MAEnE;MACAH,sBAAsB,GAAGF,eAAe,GAAGM,sBAAsB;IAClE;IAEA,IAAIC,WAAW;IACf,IAAIC,iBAAiB,GAAG,KAAK,CAAC,CAAC;;IAE/B,IAAIN,sBAAsB,EAAE;MAC3B;MACAK,WAAW,GAAGjB,aAAa,CAACH,GAAG,GAAGK,WAAW;MAC7CgB,iBAAiB,GAAG,IAAI;IACzB,CAAC,MAAM,IAAIR,eAAe,IAAIP,YAAY,EAAE;MAC3C;MACAc,WAAW,GAAGP,eAAe;IAC9B,CAAC,MAAM;MACN;MACA,MAAMS,mBAAmB,GAAGnB,aAAa,CAACH,GAAG,GAAGI,aAAa,GAAGE,YAAY;MAC5E,MAAMiB,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;MAC3B,MAAM/F,GAAG,GAAG+F,IAAI,CAACxC,IAAI,CAACyC,CAAC,IAAIF,mBAAmB,IAAIE,CAAC,CAAC,IAAI,CAAC;MACzDJ,WAAW,GAAG5F,GAAG,KAAK,CAAC,GACpB6C,IAAI,CAACG,GAAG,CAAC8B,YAAY,EAAEH,aAAa,CAACH,GAAG,GAAGI,aAAa,GAAG,CAAC,CAAC,GAC7DD,aAAa,CAACH,GAAG,GAAGI,aAAa,GAAG5E,GAAG;IAC3C;;IAEA;IACA,MAAMiG,cAAc,GAAGlB,cAAc,GAAGH,aAAa;IACrDgB,WAAW,GAAG/C,IAAI,CAACC,GAAG,CAAC8C,WAAW,EAAEK,cAAc,CAAC;;IAEnD;IACA,IAAI,CAACJ,iBAAiB,EAAE;MACvBD,WAAW,GAAG/C,IAAI,CAACG,GAAG,CAAC4C,WAAW,EAAEd,YAAY,CAAC;IAClD;;IAEA;IACA,IAAIoB,YAAY,GAAGvB,aAAa,CAACF,IAAI,GAAIE,aAAa,CAACrF,KAAK,GAAG,CAAE,GAAG,GAAG;IACvE4G,YAAY,GAAGrD,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACC,GAAG,CAACoD,YAAY,EAAEf,aAAa,GAAGD,YAAY,GAAG,EAAE,CAAC,CAAC;IAEtF,OAAO;MACNV,GAAG,EAAEoB,WAAW;MAChBnB,IAAI,EAAEyB,YAAY;MAClBC,QAAQ,EAAE,OAAgB;MAC1BC,MAAM,EAAE;IACT,CAAC;EACF,CAAC;EACD,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACxC,MAAM1B,aAAa,GAAGR,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACQ,aAAa,EAAE,OAAO,CAAC,CAAC;IAC7B,MAAMI,cAAc,GAAGC,MAAM,CAACC,WAAW;IACzC,MAAME,aAAa,GAAGH,MAAM,CAACI,UAAU;IACvC,MAAMkB,mBAAmB,GAAG,GAAG;IAC/B,MAAMC,kBAAkB,GAAG,GAAG;IAC9B,IAAIL,YAAY,GAAGvB,aAAa,CAACF,IAAI,GAAGE,aAAa,CAACrF,KAAK,GAAG,EAAE;IAChE,IAAIsG,WAAW,GAAGjB,aAAa,CAACH,GAAG,GAAIG,aAAa,CAACjH,MAAM,GAAG,CAAE,GAAI4I,mBAAmB,GAAC,CAAE,GAAG,EAAE;IAC/F,IAAIJ,YAAY,GAAGK,kBAAkB,GAAGpB,aAAa,GAAG,EAAE,EAAE;MAC3De,YAAY,GAAGvB,aAAa,CAACF,IAAI,GAAG8B,kBAAkB,GAAG,EAAE,CAAC,CAAC;IAC9D;IACAX,WAAW,GAAG/C,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACC,GAAG,CAAC8C,WAAW,EAAEb,cAAc,GAAGuB,mBAAmB,GAAG,EAAE,CAAC,CAAC;IAC5FJ,YAAY,GAAGrD,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACC,GAAG,CAACoD,YAAY,EAAEf,aAAa,GAAGoB,kBAAkB,GAAG,EAAE,CAAC,CAAC;IAC5F,OAAO;MACN/B,GAAG,EAAEoB,WAAW;MAChBnB,IAAI,EAAEyB,YAAY;MAClBC,QAAQ,EAAE,OAAgB;MAC1BC,MAAM,EAAE;IACT,CAAC;EACF,CAAC;EACD,MAAMI,sBAAsB,GAAGA,CAAA,KAAM;IACpC/H,mBAAmB,CAAC;MACnBC,YAAY,EAAEgG,uBAAuB,CAAC,CAAC;MACvC/F,eAAe,EAAE0H,0BAA0B,CAAC;IAC7C,CAAC,CAAC;EACH,CAAC;EACD5N,SAAS,CAAC,MAAM;IACf,MAAMgO,oBAAoB,GAAGA,CAAA,KAAMD,sBAAsB,CAAC,CAAC;IAC3D,IAAIjE,IAAI,IAAI3D,mBAAmB,EAAE;MAChC4H,sBAAsB,CAAC,CAAC;MACxB,MAAME,qBAAqB,GAAGC,WAAW,CAAC,MAAM;QAC/C,IAAIpE,IAAI,IAAI3D,mBAAmB,EAAE;UAChC4H,sBAAsB,CAAC,CAAC;QACzB,CAAC,MAAM;UACNI,aAAa,CAACF,qBAAqB,CAAC;QACrC;MACD,CAAC,EAAE,GAAG,CAAC;MACN1B,MAAM,CAAS6B,0BAA0B,GAAGH,qBAAqB;IACnE;IACA1B,MAAM,CAAC8B,gBAAgB,CAAC,QAAQ,EAAEL,oBAAoB,CAAC;IACvDzB,MAAM,CAAC8B,gBAAgB,CAAC,QAAQ,EAAEL,oBAAoB,CAAC;IACvD,MAAMM,uBAAuB,GAAG5D,QAAQ,CAACkB,aAAa,CAAC,wBAAwB,CAAC;IAChF,IAAI0C,uBAAuB,EAAE;MAC5BA,uBAAuB,CAACD,gBAAgB,CAAC,aAAa,EAAEL,oBAAoB,CAAC;MAC7EM,uBAAuB,CAACD,gBAAgB,CAAC,QAAQ,EAAEL,oBAAoB,CAAC;IACzE;IACA,MAAMO,iBAAiB,GAAG,CACzB7D,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,EACtCD,QAAQ,CAACkB,aAAa,CAAC,qCAAqC,CAAC,EAC7DlB,QAAQ,CAACkB,aAAa,CAAC,wBAAwB,CAAC,CAChD,CAAC4C,MAAM,CAACpI,OAAO,CAAkB;IAClC,IAAIqI,cAAqC,GAAG,IAAI;IAChD,IAAIC,gBAAyC,GAAG,IAAI;IACpD,IAAInC,MAAM,CAACoC,cAAc,IAAIJ,iBAAiB,CAACnG,MAAM,GAAG,CAAC,EAAE;MAC1DqG,cAAc,GAAG,IAAIE,cAAc,CAAEC,OAAO,IAAK;QAChD,MAAMC,oBAAoB,GAAGD,OAAO,CAACE,IAAI,CAAEC,KAAK,IAAK;UACpD,MAAM;YAAElI,KAAK;YAAE5B;UAAO,CAAC,GAAG8J,KAAK,CAACC,WAAW;UAC3C,MAAMrD,OAAO,GAAGoD,KAAK,CAACxI,MAAqB;UAC3C,MAAM0I,SAAS,GAAGC,UAAU,CAACvD,OAAO,CAACwD,OAAO,CAACF,SAAS,IAAI,GAAG,CAAC;UAC9D,MAAMG,UAAU,GAAGF,UAAU,CAACvD,OAAO,CAACwD,OAAO,CAACC,UAAU,IAAI,GAAG,CAAC;UAChE,IAAIhF,IAAI,CAACiF,GAAG,CAACxI,KAAK,GAAGoI,SAAS,CAAC,GAAG,CAAC,IAAI7E,IAAI,CAACiF,GAAG,CAACpK,MAAM,GAAGmK,UAAU,CAAC,GAAG,CAAC,EAAE;YACzEzD,OAAO,CAACwD,OAAO,CAACF,SAAS,GAAGpI,KAAK,CAACyI,QAAQ,CAAC,CAAC;YAC5C3D,OAAO,CAACwD,OAAO,CAACC,UAAU,GAAGnK,MAAM,CAACqK,QAAQ,CAAC,CAAC;YAC9C,OAAO,IAAI;UACZ;UACA,OAAO,KAAK;QACb,CAAC,CAAC;QACF,IAAIT,oBAAoB,EAAE;UACzBd,sBAAsB,CAAC,CAAC;UACxBwB,UAAU,CAACxB,sBAAsB,EAAE,EAAE,CAAC;QACvC;MACD,CAAC,CAAC;MAEFQ,iBAAiB,CAACiB,OAAO,CAAC7D,OAAO,IAAI8C,cAAc,CAAEgB,OAAO,CAAC9D,OAAO,CAAC,CAAC;IACvE;IACA,MAAM+D,iBAAiB,GAAGnB,iBAAiB,CAAC,CAAC,CAAC;IAC9C,IAAImB,iBAAiB,IAAInD,MAAM,CAACoD,gBAAgB,EAAE;MACjDjB,gBAAgB,GAAG,IAAIiB,gBAAgB,CAAEC,SAAS,IAAK;QACtD,MAAMC,YAAY,GAAGD,SAAS,CAACd,IAAI,CAAEgB,QAAQ,IAAK;UACjD,OAAOA,QAAQ,CAACC,IAAI,KAAK,WAAW,IAClCD,QAAQ,CAACC,IAAI,KAAK,YAAY,IAC9B,CAAC,OAAO,EAAE,OAAO,CAAC,CAACnG,QAAQ,CAACkG,QAAQ,CAACE,aAAa,IAAI,EAAE,CAAE;QAC7D,CAAC,CAAC;QACF,IAAIH,YAAY,EAAE;UACjBN,UAAU,CAACxB,sBAAsB,EAAE,EAAE,CAAC;QACvC;MACD,CAAC,CAAC;MACFW,gBAAgB,CAACe,OAAO,CAACC,iBAAiB,EAAE;QAC3CO,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;MACnC,CAAC,CAAC;IACH;IACA,OAAO,MAAM;MAAA,IAAAC,eAAA,EAAAC,iBAAA;MACZ/D,MAAM,CAACgE,mBAAmB,CAAC,QAAQ,EAAEvC,oBAAoB,CAAC;MAC1DzB,MAAM,CAACgE,mBAAmB,CAAC,QAAQ,EAAEvC,oBAAoB,CAAC;MAC1D,MAAMM,uBAAuB,GAAG5D,QAAQ,CAACkB,aAAa,CAAC,wBAAwB,CAAC;MAChF,IAAI0C,uBAAuB,EAAE;QAC5BA,uBAAuB,CAACiC,mBAAmB,CAAC,aAAa,EAAEvC,oBAAoB,CAAC;QAChFM,uBAAuB,CAACiC,mBAAmB,CAAC,QAAQ,EAAEvC,oBAAoB,CAAC;MAC5E;MACA,MAAMwC,UAAU,GAAIjE,MAAM,CAAS6B,0BAA0B;MAC7D,IAAIoC,UAAU,EAAE;QACfrC,aAAa,CAACqC,UAAU,CAAC;QACzB,OAAQjE,MAAM,CAAS6B,0BAA0B;MAClD;MACA,CAAAiC,eAAA,GAAA5B,cAAc,cAAA4B,eAAA,uBAAdA,eAAA,CAAgBI,UAAU,CAAC,CAAC;MAC5B,CAAAH,iBAAA,GAAA5B,gBAAgB,cAAA4B,iBAAA,uBAAhBA,iBAAA,CAAkBG,UAAU,CAAC,CAAC;IAC/B,CAAC;EACF,CAAC,EAAE,CAAC3G,IAAI,EAAE3D,mBAAmB,CAAC,CAAC;EAE/B,oBACChE,OAAA,CAAAE,SAAA;IAAAqO,QAAA,GACEzN,eAAe,CAAC0N,GAAG,CAAE5F,IAAI,IAAK;MAAA,IAAA6F,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,WAAA;MAC9B,MAAMC,QAAQ,IAAAJ,aAAA,GAAG7F,IAAI,CAACkG,MAAM,CAAC,CAAC,CAAC,cAAAL,aAAA,uBAAdA,aAAA,CAAgB7H,GAAG;MACpC,MAAMW,OAAO,IAAAmH,cAAA,GAAG9F,IAAI,CAACkG,MAAM,CAAC,CAAC,CAAC,cAAAJ,cAAA,uBAAdA,cAAA,CAAgBjI,EAAE;MAClC,MAAMK,SAAS,GAAG,EAAA6H,cAAA,GAAA/F,IAAI,CAACkG,MAAM,CAAC,CAAC,CAAC,cAAAH,cAAA,uBAAdA,cAAA,CAAgB7H,SAAS,KAAInH,cAAc;MAC7D,MAAMoP,gBAAgB,GAAG,CAACnG,IAAI,aAAJA,IAAI,wBAAAgG,WAAA,GAAJhG,IAAI,CAAEC,KAAK,cAAA+F,WAAA,uBAAXA,WAAA,CAAa9L,MAAM,KAAetD,4BAA4B;MACxF,MAAMiH,EAAE,GAAGmC,IAAI,CAACnC,EAAE;MAClB,oBACCzG,OAAA,CAAChC,GAAG;QAEHgR,EAAE,EAAE;UACHtK,KAAK,EAAE,MAAM;UACb5B,MAAM,EAAE,MAAM;UACd6B,OAAO,EAAE,MAAM;UACfY,aAAa,EAAE,QAAQ;UACvBX,cAAc,EAAE,YAAY;UAC5BC,UAAU,EAAE,QAAQ;UACpBE,MAAM,EAAE,KAAK;UACbC,QAAQ,EAAE;QACX,CAAE;QAAAuJ,QAAA,eAEFvO,OAAA,CAAChC,GAAG;UACHgR,EAAE,EAAE;YACH,GAAGvK,mBAAmB;YACtBoC,eAAe,EAAE+B,IAAI,CAACC,KAAK,CAAChC,eAAe;YAC3C/D,MAAM,EAAE,GAAG8F,IAAI,CAACC,KAAK,CAAC/F,MAAM;UAC7B,CAAE;UACFmM,OAAO,EAAGC,CAAC,IAAK5H,WAAW,CAAC4H,CAAC,EAAEzI,EAAE,EAAEc,OAAO,EAAEsH,QAAQ,GAAG,IAAI,GAAG,KAAK,EAAEE,gBAAgB,CAAE;UACvFI,SAAS,EAAE,KAAM;UACjB1I,EAAE,EAAEA,EAAG;UACP2I,WAAW,EAAEA,CAAA,KAAM;YAClBpO,gBAAgB,CAAC;cAChBkG,QAAQ,EAAEK,OAAO;cACjBhB,WAAW,EAAEE,EAAE;cACfpC,KAAK,EAAE;YACR,CAAC,CAAC;UACH,CAAE;UAAAkK,QAAA,EAEDM,QAAQ,gBACR7O,OAAA;YACCqP,GAAG,EAAER,QAAS;YACdS,GAAG,EAAC,UAAU;YACdzG,KAAK,EAAE;cAAE,GAAG5D,UAAU;cAAE6B;YAAU;UAAE;YAAAyI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,gBAEF1P,OAAA,CAAChC,GAAG;YACHgR,EAAE,EAAE;cACHW,SAAS,EAAE,QAAQ;cACnBjL,KAAK,EAAE,MAAM;cACb5B,MAAM,EAAE,MAAM;cACd6B,OAAO,EAAE,MAAM;cACfY,aAAa,EAAE,QAAQ;cACvBX,cAAc,EAAE;YACjB,CAAE;YAAA2J,QAAA,gBAEFvO,OAAA,CAAChC,GAAG;cACHgR,EAAE,EAAE1J,aAAc;cAClB6J,SAAS,EAAE,KAAM;cAAAZ,QAAA,gBAEjBvO,OAAA;gBACC4P,uBAAuB,EAAE;kBAAEC,MAAM,EAAEhR;gBAAW,CAAE;gBAChDgK,KAAK,EAAE;kBAAElE,OAAO,EAAE;gBAAe;cAAE;gBAAA4K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACF1P,OAAA,CAAC/B,UAAU;gBACV6R,OAAO,EAAC,IAAI;gBACZC,KAAK,EAAC,QAAQ;gBACdf,EAAE,EAAE;kBAAEgB,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAM,CAAE;gBAAA1B,QAAA,EAE1C3N,SAAS,CAAC,aAAa;cAAC;gBAAA2O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEN1P,OAAA,CAAC/B,UAAU;cACV6R,OAAO,EAAC,OAAO;cACfC,KAAK,EAAC,QAAQ;cACd3G,KAAK,EAAC,eAAe;cACrB4F,EAAE,EAAE;gBAAEgB,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,EAEvB3N,SAAS,CAAC,4BAA4B;YAAC;cAAA2O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACb1P,OAAA,CAAC/B,UAAU;cACV6R,OAAO,EAAC,OAAO;cACfC,KAAK,EAAC,QAAQ;cACd3G,KAAK,EAAC,eAAe;cACrB4F,EAAE,EAAE;gBAAE3J,SAAS,EAAE,KAAK;gBAAE2K,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,EAEzC3N,SAAS,CAAC,IAAI;YAAC;cAAA2O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACZvN,kBAAkB,CAACG,MAAM,IAAIH,kBAAkB,CAACE,kBAAkB,KAAKoE,EAAE,gBACzEzG,OAAA,CAAC5B,SAAS;cACTiG,KAAK,EAAE9B,SAAU;cACjB2N,QAAQ,EAAGhB,CAAC,IAAK1M,YAAY,CAAC0M,CAAC,CAAC9K,MAAM,CAACC,KAAK,CAAE;cAC9C8L,SAAS,EAAEpH,gBAAiB;cAC5BqH,SAAS;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,gBAEF1P,OAAA,CAAChC,GAAG;cAACgR,EAAE,EAAE7J,YAAa;cAAAoJ,QAAA,gBACnBvO,OAAA,CAACzB,OAAO;gBAAC8R,KAAK,EAAEzP,SAAS,CAAC,aAAa,CAAE;gBAAA2N,QAAA,eACpDvO,OAAA;kBAAK6I,KAAK,EAAE;oBAAEyH,aAAa,EAAE,MAAM;oBAAEC,MAAM,EAAC;kBAAS,CAAE;kBAAAhC,QAAA,eACrDvO,OAAA;oBACE4P,uBAAuB,EAAE;sBAAEC,MAAM,EAAE/Q;oBAAU,CAAE;oBAC/C+J,KAAK,EAAE;sBACLO,KAAK,EAAE,OAAO;sBACdmH,MAAM,EAAE,SAAS;sBACjBP,QAAQ,EAAE,MAAM;sBAChBQ,OAAO,EAAE,KAAK;sBACdF,aAAa,EAAE;oBACjB,CAAE;oBACF7J,EAAE,EAAC,WAAW;oBACPgK,SAAS,EAAC;kBAAoB;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGG1P,OAAA,CAACzB,OAAO;gBAAC8R,KAAK,EAAEzP,SAAS,CAAC,aAAa,CAAE;gBAAA2N,QAAA,eACxCvO,OAAA;kBACCiP,OAAO,EAAG9K,KAAK,IAAK;oBACnB;kBAAA,CACC;kBACLyL,uBAAuB,EAAE;oBAAEC,MAAM,EAAE9Q;kBAAM,CAAE;kBAC3C8J,KAAK,EAAE;oBAAEO,KAAK,EAAE,OAAO;oBAAEmH,MAAM,EAAE,SAAS;oBAAEP,QAAQ,EAAE,MAAM;oBAAEQ,OAAO,EAAE;kBAAM,CAAE;kBAC/E/J,EAAE,EAAC,QAAQ;kBACXgK,SAAS,EAAC;kBACV;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACV1P,OAAA,CAACzB,OAAO;gBAAC8R,KAAK,EAAEzP,SAAS,CAAC,aAAa,CAAE;gBAAA2N,QAAA,eAC3CvO,OAAA;kBACIiP,OAAO,EAAG9K,KAAK,IAAK;oBAAA,IAAAuM,sBAAA;oBAEtBvM,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwM,eAAe,CAAC,CAAC;oBACxB,CAAAD,sBAAA,GAAAnI,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,cAAAkI,sBAAA,uBAAtCA,sBAAA,CAAwCjI,KAAK,CAAC,CAAC;kBAChD,CAAE;kBACFhC,EAAE,EAAC,cAAc;kBACjBgK,SAAS,EAAC,oBAAoB;kBAC9Bb,uBAAuB,EAAE;oBAAEC,MAAM,EAAE7Q;kBAAW,CAAE;kBAChD6J,KAAK,EAAE;oBAAEO,KAAK,EAAE,OAAO;oBAAEmH,MAAM,EAAE,SAAS;oBAAEP,QAAQ,EAAE;kBAAO;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACb1P,OAAA;gBACC4N,IAAI,EAAC,MAAM;gBACXnH,EAAE,EAAC,aAAa;gBAChBoC,KAAK,EAAE;kBAAElE,OAAO,EAAE;gBAAO,CAAE;gBAC3BiM,MAAM,EAAC,SAAS;gBAChBV,QAAQ,EAAE1K;cAAkB;gBAAA+J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACF1P,OAAA,CAACxB,QAAQ;gBAACmJ,IAAI,EAAEnG,YAAa;gBAACqP,gBAAgB,EAAE,IAAK;gBAACC,OAAO,EAAE5O,aAAc;gBAAC6O,YAAY,EAAE;kBAAEC,QAAQ,EAAE,QAAQ;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAA1C,QAAA,eACxIvO,OAAA,CAACvB,KAAK;kBAACqS,OAAO,EAAE5O,aAAc;kBAACgP,QAAQ,EAAEtP,gBAAiB;kBAACoN,EAAE,EAAE;oBAAEtK,KAAK,EAAE;kBAAO,CAAE;kBAAA6J,QAAA,EAC/E7M;gBAAe;kBAAA6N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC,GAnJDjJ,EAAE;QAAA8I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoJH,CAAC;IAER,CAAC,CAAC,EACDzL,OAAO,CAAClD,aAAa,CAACsD,KAAK,CAAC,gBAC5BrE,OAAA,CAAC9B,OAAO;MACPuS,SAAS,EAAC,sBAAsB;MAChChK,EAAE,EAAEA,EAAG;MACPkB,IAAI,EAAEA,IAAK;MACXwJ,QAAQ,EAAE,IAAK;MACfL,OAAO,EAAEpJ,WAAY;MACrB0J,eAAe,EAAC,MAAM;MACtBC,SAAS,EAAE;QACVC,KAAK,EAAE;UACNzI,KAAK,EAAE;YACN,GAAGjF,gBAAgB,CAACE,YAAY;YAChChB,MAAM,EAAE,MAAM;YACd4B,KAAK,EAAE,MAAM;YACbI,OAAO,EAAE,UAAU;YACnByM,UAAU,EAAE,MAAM;YAClBC,WAAW,EAAE;UACd;QACD;MACD,CAAE;MAAAjD,QAAA,eAEFvO,OAAA,CAAChC,GAAG;QACHgR,EAAE,EAAE;UACHrK,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBO,GAAG,EAAE,MAAM;UACXtC,MAAM,EAAE,MAAM;UACdgC,OAAO,EAAE,QAAQ;UACjBkL,QAAQ,EAAE;QACX,CAAE;QAAAzB,QAAA,gBAEFvO,OAAA,CAAChC,GAAG;UAACgR,EAAE,EAAE;YAAErK,OAAO,EAAE;UAAO,CAAE;UAAA4J,QAAA,EAC3B5L,uBAAuB,CAACN,kBAAkB,KAAKtB,aAAa,CAACwF,WAAW,IACzE5D,uBAAuB,CAACE,OAAO,gBAC9B7C,OAAA,CAAAE,SAAA;YAAAqO,QAAA,gBACCvO,OAAA;cAAM4P,uBAAuB,EAAE;gBAAEC,MAAM,EAAE5Q;cAAiB;YAAE;cAAAsQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/D1P,OAAA,CAAC/B,UAAU;cACV+R,QAAQ,EAAC,MAAM;cACfuB,UAAU,EAAE,KAAM;cAClBtC,OAAO,EAAE5G,kBAAmB;cAAAkG,QAAA,EAE1B3N,SAAS,CAAC,eAAe;YAAC;cAAA2O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACb1P,OAAA;cACC4N,IAAI,EAAC,MAAM;cACXnH,EAAE,EAAC,gBAAgB;cACnBoC,KAAK,EAAE;gBAAElE,OAAO,EAAE;cAAO,CAAE;cAC3BiM,MAAM,EAAC,SAAS;cAChBV,QAAQ,EAAE9I;YAAmB;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA,eACD,CAAC,GACA;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEZ1P,OAAA,CAAChC,GAAG;UACGyS,SAAS,EAAC,kBAAkB;UAC5BzB,EAAE,EAAE;YAAErK,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAA0J,QAAA,gBAE9CvO,OAAA;YAAM4P,uBAAuB,EAAE;cAAEC,MAAM,EAAEzQ;YAAc,CAAE;YACzDyJ,KAAK,EAAE;cAAElE,OAAO,EAAE;YAAO;UAAE;YAAA4K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC7B1P,OAAA,CAACzB,OAAO;YAAC8R,KAAK,EAAE1N,uBAAuB,CAACG,MAAM,IAAIpD,wBAAwB,GAAGkB,SAAS,CAAC,wBAAwB,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAE;YAAA2N,QAAA,eAC/IvO,OAAA;cAAAuO,QAAA,eACCvO,OAAA,CAAC7B,UAAU;gBACV8Q,OAAO,EAAEA,CAAA,KAAM9G,oBAAoB,CAACxF,uBAAuB,CAACG,MAAM,CAAE;gBACpE2O,IAAI,EAAC,OAAO;gBACZC,QAAQ,EAAE/O,uBAAuB,CAACG,MAAM,IAAIpD,wBAAyB;gBACrEsP,EAAE,EAAE;kBACHwB,OAAO,EAAE7N,uBAAuB,CAACG,MAAM,IAAIpD,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7E6Q,MAAM,EAAE5N,uBAAuB,CAACG,MAAM,IAAIpD,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAA6O,QAAA,eAEFvO,OAAA,CAACtB,UAAU;kBAACsR,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACV1P,OAAA,CAAC/B,UAAU;YAAC+R,QAAQ,EAAC,MAAM;YAAAzB,QAAA,EAAE5L,uBAAuB,CAACG;UAAM;YAAAyM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzE1P,OAAA,CAACzB,OAAO;YAAC8R,KAAK,EAAE1N,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAGmB,SAAS,CAAC,wBAAwB,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAE;YAAA2N,QAAA,eAC/IvO,OAAA;cAAAuO,QAAA,eACCvO,OAAA,CAAC7B,UAAU;gBACV8Q,OAAO,EAAEA,CAAA,KAAMnH,oBAAoB,CAACnF,uBAAuB,CAACG,MAAM,CAAE;gBACpE2O,IAAI,EAAC,OAAO;gBACZC,QAAQ,EAAE/O,uBAAuB,CAACG,MAAM,IAAIrD,wBAAyB;gBACrEuP,EAAE,EAAE;kBACHwB,OAAO,EAAE7N,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7E8Q,MAAM,EAAE5N,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAA8O,QAAA,eAEFvO,OAAA,CAACrB,OAAO;kBAACqR,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1P,OAAA,CAACzB,OAAO;UAAC8R,KAAK,EAAEzP,SAAS,CAAC,UAAU,CAAE;UAAA2N,QAAA,eACtCvO,OAAA,CAAChC,GAAG;YAACyS,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,gBAChCvO,OAAA,CAAChC,GAAG;cAACyS,SAAS,EAAC,kBAAkB;cAAAlC,QAAA,eAChCvO,OAAA,CAAC7B,UAAU;gBACVsT,IAAI,EAAC,OAAO;gBACZxC,OAAO,EAAE3K,mBAAoB;gBAAAiK,QAAA,eAE7BvO,OAAA;kBACC4P,uBAAuB,EAAE;oBAAEC,MAAM,EAAExQ;kBAAS,CAAE;kBAC9CwJ,KAAK,EAAE;oBAAEO,KAAK,EAAE;kBAAO;gBAAE;kBAAAmG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEL1P,OAAA,CAAC9B,OAAO;cACEuS,SAAS,EAAC,cAAc;cAClC9I,IAAI,EAAE3D,mBAAoB;cAC1BmN,QAAQ,EAAE,IAAK;cACfL,OAAO,EAAEtM,0BAA2B;cACpC4M,eAAe,EAAC,MAAM;cACtBC,SAAS,EAAE;gBACVC,KAAK,EAAE;kBACNzI,KAAK,EAAE;oBACN,GAAGjF,gBAAgB,CAACG,eAAe;oBACnCW,KAAK,EAAE;kBACR;gBACD;cACD,CAAE;cAAA6J,QAAA,eAEFvO,OAAA,CAAChC,GAAG;gBAAC2T,CAAC,EAAE,CAAE;gBAAApD,QAAA,gBACTvO,OAAA,CAAChC,GAAG;kBACH2G,OAAO,EAAC,MAAM;kBACdC,cAAc,EAAC,eAAe;kBAC9BC,UAAU,EAAC,QAAQ;kBAAA0J,QAAA,gBAEnBvO,OAAA,CAAC/B,UAAU;oBACV6R,OAAO,EAAC,WAAW;oBACnBd,EAAE,EAAE;sBAAE5F,KAAK,EAAE;oBAAwB,CAAE;oBAAAmF,QAAA,EAErC3N,SAAS,CAAC,kBAAkB;kBAAC;oBAAA2O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACb1P,OAAA,CAAC7B,UAAU;oBACVsT,IAAI,EAAC,OAAO;oBACZxC,OAAO,EAAEzK,0BAA2B;oBAAA+J,QAAA,eAEpCvO,OAAA;sBACC4P,uBAAuB,EAAE;wBAAEC,MAAM,EAAEvQ;sBAAU,CAAE;sBAC/CuJ,KAAK,EAAE;wBAAEO,KAAK,EAAE;sBAAQ;oBAAE;sBAAAmG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACN1P,OAAA,CAACzB,OAAO;kBAAC8R,KAAK,EAAEzP,SAAS,CAAC,aAAa,CAAE;kBAAA2N,QAAA,eAC1CvO,OAAA,CAAChC,GAAG;oBAAC4T,EAAE,EAAE,CAAE;oBAAArD,QAAA,gBACVvO,OAAA,CAAC/B,UAAU;sBACV6R,OAAO,EAAC,OAAO;sBACb1G,KAAK,EAAC,eAAe;sBACrB4F,EAAE,EAAE;wBAAE6C,YAAY,EAAE;sBAAO,CAAE;sBAAAtD,QAAA,EAE5B3N,SAAS,CAAC,eAAe;oBAAC;sBAAA2O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACb1P,OAAA,CAAC5B,SAAS;sBACT0T,MAAM;sBACNC,SAAS;sBACTjC,OAAO,EAAC,UAAU;sBAClB2B,IAAI,EAAC,OAAO;sBACZpN,KAAK,EAAEtB,cAAe;sBACtBmN,QAAQ,EAAEhM,kBAAmB;sBAC7B8K,EAAE,EAAE;wBACH,0BAA0B,EAAE;0BAC3BgD,WAAW,EAAE;wBACd;sBACD,CAAE;sBACFN,QAAQ;sBAAAnD,QAAA,gBAENvO,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,MAAM;wBAAAkK,QAAA,EAAE3N,SAAS,CAAC,MAAM;sBAAC;wBAAA2O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACrD1P,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,cAAc;wBAAAkK,QAAA,EAAE3N,SAAS,CAAC,eAAe;sBAAC;wBAAA2O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtE1P,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,SAAS;wBAAAkK,QAAA,EAAE3N,SAAS,CAAC,UAAU;sBAAC;wBAAA2O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAC5D1P,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,cAAc;wBAAAkK,QAAA,EAAE3N,SAAS,CAAC,eAAe;sBAAC;wBAAA2O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtE1P,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,WAAW;wBAAAkK,QAAA,EAAE3N,SAAS,CAAC,YAAY;sBAAC;wBAAA2O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAChE1P,OAAA,CAAC3B,QAAQ;wBAACgG,KAAK,EAAC,kBAAkB;wBAAAkK,QAAA,EAAE3N,SAAS,CAAC,oBAAoB;sBAAC;wBAAA2O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACV1P,OAAA,CAAChC,GAAG;kBAAC4T,EAAE,EAAE,CAAE;kBAAArD,QAAA,gBACVvO,OAAA,CAAC/B,UAAU;oBACV6R,OAAO,EAAC,OAAO;oBACf1G,KAAK,EAAC,eAAe;oBAAAmF,QAAA,EAEnB3N,SAAS,CAAC,kBAAkB;kBAAC;oBAAA2O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACb1P,OAAA,CAAChC,GAAG;oBACH2G,OAAO,EAAC,MAAM;oBACdS,GAAG,EAAE,CAAE;oBACPwM,EAAE,EAAE,CAAE;oBAAArD,QAAA,EAEL,CAAC,MAAM,EAAE,KAAK,CAAC,CAACC,GAAG,CAAE5F,IAAI,IAAK;sBAC9B;sBACA,MAAMqJ,gBAAgB,GAAGnR,eAAe,CAAC6H,IAAI,CAAEuJ,CAAC,IAAKA,CAAC,CAACzL,EAAE,KAAK1F,aAAa,CAACwF,WAAW,CAAC;sBACxF,MAAM4L,YAAY,GAAGF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEnD,MAAM,CAACnG,IAAI,CAAEyJ,GAAG,IAAKA,GAAG,CAAC3L,EAAE,KAAK1F,aAAa,CAACmG,QAAQ,CAAC;sBAC9F,MAAMmL,gBAAgB,GAAG,CAAAF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAErL,SAAS,KAAInH,cAAc;;sBAElE;sBACA,MAAM2S,UAAU,GAAI1J,IAAI,KAAK,MAAM,IAAIyJ,gBAAgB,KAAK,OAAO,IAC5DzJ,IAAI,KAAK,KAAK,IAAIyJ,gBAAgB,KAAK,SAAU;sBAExD,oBACCrS,OAAA,CAAC1B,MAAM;wBAEN2Q,OAAO,EAAEA,CAAA,KACR5N,SAAS,CAACN,aAAa,CAACwF,WAAW,EAAExF,aAAa,CAACmG,QAAQ,EAAE0B,IAAsB,CACnF;wBACDkH,OAAO,EAAC,UAAU;wBAClB2B,IAAI,EAAC,OAAO;wBACZzC,EAAE,EAAE;0BACHtK,KAAK,EAAE,QAAQ;0BACf5B,MAAM,EAAE,MAAM;0BACdgC,OAAO,EAAE,WAAW;0BACpBM,GAAG,EAAE,MAAM;0BACXF,YAAY,EAAE,iBAAiB;0BAC/BqN,MAAM,EACLD,UAAU,GACP,iCAAiC,GACjC,kCAAkC;0BACtCzL,eAAe,EACdyL,UAAU,GAAG,yBAAyB,GAAG,0BAA0B;0BACpEE,mBAAmB,EAAE,UAAU;0BAC/BpJ,KAAK,EAAE,OAAO;0BACd,SAAS,EAAE;4BACVvC,eAAe,EACdyL,UAAU,GAAG,yBAAyB,GAAG;0BAC3C;wBACD,CAAE;wBAAA/D,QAAA,EAED3N,SAAS,CAACgI,IAAI;sBAAC,GA1BXA,IAAI;wBAAA2G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2BF,CAAC;oBAEX,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACV1P,OAAA,CAACzB,OAAO;UAAC8R,KAAK,EAAEzP,SAAS,CAAC,kBAAkB,CAAE;UAAA2N,QAAA,eAC9CvO,OAAA,CAAChC,GAAG;YAACyS,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChCvO,OAAA,CAAC7B,UAAU;cACV8Q,OAAO,EAAE3F,0BAA2B;cACpCmI,IAAI,EAAC,OAAO;cAAAlD,QAAA,eAEZvO,OAAA;gBACA6I,KAAK,EAAE;kBACNhC,eAAe,EAAEtD,aAAa;kBAC9B2B,YAAY,EAAE,MAAM;kBACpBR,KAAK,EAAE,MAAM;kBACb5B,MAAM,EAAE;gBACT;cAAE;gBAAAyM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACV1P,OAAA,CAACzB,OAAO;UAAC8R,KAAK,EAAE7P,eAAe,GAAGI,SAAS,CAAC,2CAA2C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;UAAA2N,QAAA,eACtHvO,OAAA,CAAChC,GAAG;YAACyS,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChCvO,OAAA,CAAC7B,UAAU;cACV8Q,OAAO,EAAEhG,uBAAwB;cACjCwI,IAAI,EAAC,OAAO;cACZC,QAAQ,EAAElR,eAAgB;cAAA+N,QAAA,eAE1BvO,OAAA;gBACC4P,uBAAuB,EAAE;kBAAEC,MAAM,EAAE3Q;gBAAS,CAAE;gBAC9C2J,KAAK,EAAE;kBAAE2H,OAAO,EAAEhQ,eAAe,GAAG,GAAG,GAAG;gBAAE;cAAE;gBAAA+O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACV1P,OAAA,CAACzB,OAAO;UAAC8R,KAAK,EAAEzP,SAAS,CAAC,gBAAgB,CAAE;UAAA2N,QAAA,eAE5CvO,OAAA,CAAChC,GAAG;YAACyS,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChCvO,OAAA,CAAC7B,UAAU;cACV8Q,OAAO,EAAEnG,mBAAoB;cAC7B2I,IAAI,EAAC,OAAO;cAAAlD,QAAA,eAEZvO,OAAA;gBAAM4P,uBAAuB,EAAE;kBAAEC,MAAM,EAAE1Q;gBAAW;cAAE;gBAAAoQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GACP,IAAI,EAEPzM,WAAW,iBACVjD,OAAA,CAACF,0BAA0B;MAACwC,MAAM,EAAEW,WAAY;MAACwP,gBAAgB,EAAEA,CAAA,KAAMvP,YAAY,CAAC,KAAK,CAAE;MAACwP,aAAa,EAAE1L,wBAAyB;MAAC5D,eAAe,EAAEA,eAAgB;MAACD,YAAY,EAAEA,YAAa;MAACiE,kBAAkB,EAAEA,kBAAmB;MAAC3D,cAAc,EAAEA;IAAe;MAAA8L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAC7Q,eAEF1P,OAAA,CAAC9B,OAAO;MACPyJ,IAAI,EAAEC,eAAgB;MACtBuJ,QAAQ,EAAE1O,mBAAoB;MAC9BqO,OAAO,EAAE5H,sBAAuB;MAChC6H,YAAY,EAAE;QACbC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACF0B,eAAe,EAAE;QAChB3B,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MAAA1C,QAAA,eAEFvO,OAAA,CAAChC,GAAG;QAAAuQ,QAAA,gBACHvO,OAAA,CAACH,YAAY;UACZuJ,KAAK,EAAEV,qBAAsB;UAC7BwH,QAAQ,EAAE/G;QAAkB;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACA1P,OAAA;UAAAuO,QAAA,EACF;AACL;AACA;AACA;AACA;QAAK;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACT,CAAC;AAEL,CAAC;AAACjP,EAAA,CA59BIN,YAAY;EAAA,QACQvB,cAAc,EAYnCW,cAAc;AAAA;AAAAqT,EAAA,GAbbzS,YAAY;AA89BlB,eAAeA,YAAY;AAAC,IAAAyS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}